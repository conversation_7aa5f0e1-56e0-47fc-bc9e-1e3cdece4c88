// 全局变量
let employees = [];
let currentSchedule = null;
let currentWeek = new Date();

// DOM元素
const elements = {
    navTabs: document.querySelectorAll('.nav-tab'),
    tabContents: document.querySelectorAll('.tab-content'),
    employeesList: document.getElementById('employeesList'),
    addEmployeeBtn: document.getElementById('addEmployeeBtn'),
    addEmployeeModal: document.getElementById('addEmployeeModal'),
    editEmployeeModal: document.getElementById('editEmployeeModal'),
    addEmployeeForm: document.getElementById('addEmployeeForm'),
    editEmployeeForm: document.getElementById('editEmployeeForm'),
    generateBtn: document.getElementById('generateBtn'),
    scheduleTable: document.getElementById('scheduleTable'),
    statsTable: document.getElementById('statsTable'),
    validationResult: document.getElementById('validationResult'),
    currentWeekSpan: document.getElementById('currentWeek'),
    prevWeekBtn: document.getElementById('prevWeekBtn'),
    nextWeekBtn: document.getElementById('nextWeekBtn'),
    loading: document.getElementById('loading'),
    message: document.getElementById('message')
};

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initEventListeners();
    loadEmployees();
    updateCurrentWeekDisplay();
});

// 初始化事件监听器
function initEventListeners() {
    // 导航标签切换
    elements.navTabs.forEach(tab => {
        tab.addEventListener('click', () => switchTab(tab.dataset.tab));
    });

    // 添加员工
    elements.addEmployeeBtn.addEventListener('click', () => showModal('addEmployeeModal'));
    elements.addEmployeeForm.addEventListener('submit', handleAddEmployee);

    // 编辑员工
    elements.editEmployeeForm.addEventListener('submit', handleEditEmployee);

    // 生成排班
    elements.generateBtn.addEventListener('click', generateSchedule);

    // 周切换
    elements.prevWeekBtn.addEventListener('click', () => changeWeek(-1));
    elements.nextWeekBtn.addEventListener('click', () => changeWeek(1));

    // 模态框关闭
    document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', closeModals);
    });

    // 点击模态框外部关闭
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModals();
        });
    });
}

// 切换标签页
function switchTab(tabName) {
    // 更新导航标签状态
    elements.navTabs.forEach(tab => {
        tab.classList.toggle('active', tab.dataset.tab === tabName);
    });

    // 更新内容区域
    elements.tabContents.forEach(content => {
        content.classList.toggle('active', content.id === `${tabName}-tab`);
    });

    // 根据标签页加载相应数据
    if (tabName === 'stats' && currentSchedule) {
        loadStats();
    }
}

// 显示模态框
function showModal(modalId) {
    document.getElementById(modalId).classList.add('show');
}

// 关闭所有模态框
function closeModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.remove('show');
    });
    // 清空表单
    document.querySelectorAll('form').forEach(form => form.reset());
}

// 显示加载状态
function showLoading() {
    elements.loading.classList.add('show');
}

// 隐藏加载状态
function hideLoading() {
    elements.loading.classList.remove('show');
}

// 显示消息
function showMessage(text, type = 'success') {
    elements.message.textContent = text;
    elements.message.className = `message ${type} show`;
    
    setTimeout(() => {
        elements.message.classList.remove('show');
    }, 3000);
}

// API请求封装
async function apiRequest(url, options = {}) {
    try {
        showLoading();
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || '请求失败');
        }
        
        return data;
    } catch (error) {
        showMessage(error.message, 'error');
        throw error;
    } finally {
        hideLoading();
    }
}

// 加载员工列表
async function loadEmployees() {
    try {
        employees = await apiRequest('/api/employees');
        renderEmployeesList();
    } catch (error) {
        console.error('加载员工失败:', error);
    }
}

// 渲染员工列表
function renderEmployeesList() {
    if (employees.length === 0) {
        elements.employeesList.innerHTML = '<div class="empty-state">暂无员工，请添加员工后开始排班</div>';
        return;
    }

    elements.employeesList.innerHTML = employees.map(emp => `
        <div class="employee-item">
            <div class="employee-info">
                <span class="employee-name">${emp.name}</span>
                <span class="employee-status ${emp.is_active ? 'active' : 'inactive'}">
                    ${emp.is_active ? '已启用' : '已停用'}
                </span>
            </div>
            <div class="employee-actions">
                <button class="btn btn-sm ${emp.is_active ? 'btn-warning' : 'btn-success'}" 
                        onclick="toggleEmployeeStatus('${emp.id}')">
                    <i class="fas ${emp.is_active ? 'fa-pause' : 'fa-play'}"></i>
                    ${emp.is_active ? '停用' : '启用'}
                </button>
                <button class="btn btn-sm btn-primary" onclick="editEmployee('${emp.id}')">
                    <i class="fas fa-edit"></i>
                    编辑
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteEmployee('${emp.id}')">
                    <i class="fas fa-trash"></i>
                    删除
                </button>
            </div>
        </div>
    `).join('');
}

// 添加员工
async function handleAddEmployee(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const name = formData.get('name').trim();
    
    if (!name) {
        showMessage('请输入员工姓名', 'error');
        return;
    }
    
    try {
        await apiRequest('/api/employees', {
            method: 'POST',
            body: JSON.stringify({ name })
        });
        
        showMessage('员工添加成功');
        closeModals();
        loadEmployees();
    } catch (error) {
        console.error('添加员工失败:', error);
    }
}

// 编辑员工
function editEmployee(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;
    
    document.getElementById('editEmployeeId').value = employee.id;
    document.getElementById('editEmployeeName').value = employee.name;
    showModal('editEmployeeModal');
}

// 处理编辑员工
async function handleEditEmployee(e) {
    e.preventDefault();
    
    const employeeId = document.getElementById('editEmployeeId').value;
    const name = document.getElementById('editEmployeeName').value.trim();
    
    if (!name) {
        showMessage('请输入员工姓名', 'error');
        return;
    }
    
    try {
        await apiRequest(`/api/employees/${employeeId}`, {
            method: 'PUT',
            body: JSON.stringify({ name })
        });
        
        showMessage('员工信息更新成功');
        closeModals();
        loadEmployees();
    } catch (error) {
        console.error('更新员工失败:', error);
    }
}

// 切换员工状态
async function toggleEmployeeStatus(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;
    
    try {
        await apiRequest(`/api/employees/${employeeId}`, {
            method: 'PUT',
            body: JSON.stringify({ is_active: !employee.is_active })
        });
        
        showMessage(`员工${employee.is_active ? '停用' : '启用'}成功`);
        loadEmployees();
    } catch (error) {
        console.error('更新员工状态失败:', error);
    }
}

// 删除员工
async function deleteEmployee(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;
    
    if (!confirm(`确定要删除员工"${employee.name}"吗？`)) return;
    
    try {
        await apiRequest(`/api/employees/${employeeId}`, {
            method: 'DELETE'
        });
        
        showMessage('员工删除成功');
        loadEmployees();
    } catch (error) {
        console.error('删除员工失败:', error);
    }
}

// 生成排班
async function generateSchedule() {
    const activeEmployees = employees.filter(emp => emp.is_active);
    
    if (activeEmployees.length === 0) {
        showMessage('没有活跃的员工，请先添加并启用员工', 'error');
        return;
    }
    
    try {
        currentSchedule = await apiRequest('/api/schedule/generate', {
            method: 'POST',
            body: JSON.stringify({
                week_start: getWeekStart(currentWeek).toISOString()
            })
        });
        
        showMessage('排班生成成功');
        renderScheduleTable();
        switchTab('schedule');
    } catch (error) {
        console.error('生成排班失败:', error);
    }
}

// 获取周开始日期（周一）
function getWeekStart(date) {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1);
    return new Date(d.setDate(diff));
}

// 更新当前周显示
function updateCurrentWeekDisplay() {
    const weekStart = getWeekStart(currentWeek);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);
    
    elements.currentWeekSpan.textContent = 
        `${weekStart.toLocaleDateString('zh-CN')} - ${weekEnd.toLocaleDateString('zh-CN')}`;
}

// 切换周
function changeWeek(direction) {
    currentWeek.setDate(currentWeek.getDate() + (direction * 7));
    updateCurrentWeekDisplay();

    // 如果有排班数据，重新生成
    if (currentSchedule) {
        generateSchedule();
    }
}

// 渲染排班表
function renderScheduleTable() {
    if (!currentSchedule) {
        elements.scheduleTable.innerHTML = '<div class="empty-state">请先生成排班表</div>';
        return;
    }

    const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    const shiftTypes = {
        'early': '早班(16:30)',
        'late': '晚班(20:30)',
        'off': '休息'
    };

    // 创建表格头部
    let tableHTML = `
        <table>
            <thead>
                <tr>
                    <th>员工</th>
                    ${currentSchedule.days.map((day, index) => {
                        const date = new Date(day.date);
                        const isWeekend = day.weekday === 'sunday';
                        return `<th class="${isWeekend ? 'sunday' : ''}">
                            <div>${date.getMonth() + 1}/${date.getDate()}</div>
                            <div>${weekdays[index]}</div>
                        </th>`;
                    }).join('')}
                </tr>
            </thead>
            <tbody>
    `;

    // 创建员工行
    const activeEmployees = employees.filter(emp => emp.is_active);
    activeEmployees.forEach(employee => {
        tableHTML += `<tr>
            <td class="employee-name">${employee.name}</td>
            ${currentSchedule.days.map((day, dayIndex) => {
                const shift = day.assignments[employee.id] || 'off';
                const isWeekend = day.weekday === 'sunday';

                if (isWeekend) {
                    return `<td class="sunday">休息</td>`;
                } else {
                    return `<td>
                        <select class="shift-select shift-${shift}"
                                onchange="updateShift('${employee.id}', ${dayIndex}, this.value)">
                            <option value="early" ${shift === 'early' ? 'selected' : ''}>早班(16:30)</option>
                            <option value="late" ${shift === 'late' ? 'selected' : ''}>晚班(20:30)</option>
                            <option value="off" ${shift === 'off' ? 'selected' : ''}>休息</option>
                        </select>
                    </td>`;
                }
            }).join('')}
        </tr>`;
    });

    tableHTML += '</tbody></table>';
    elements.scheduleTable.innerHTML = tableHTML;
}

// 更新班次
function updateShift(employeeId, dayIndex, newShift) {
    if (!currentSchedule) return;

    currentSchedule.days[dayIndex].assignments[employeeId] = newShift;

    // 更新选择框样式
    const selectElement = event.target;
    selectElement.className = `shift-select shift-${newShift}`;

    showMessage('班次更新成功');
}

// 加载统计数据
async function loadStats() {
    if (!currentSchedule) {
        elements.validationResult.innerHTML = '<div class="empty-state">请先生成排班表</div>';
        elements.statsTable.innerHTML = '';
        return;
    }

    try {
        // 获取验证结果
        const validation = await apiRequest('/api/schedule/validate', {
            method: 'POST',
            body: JSON.stringify({ schedule: currentSchedule })
        });

        // 获取统计数据
        const stats = await apiRequest('/api/schedule/stats', {
            method: 'POST',
            body: JSON.stringify({ schedule: currentSchedule })
        });

        renderValidationResult(validation);
        renderStatsTable(stats);
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// 渲染验证结果
function renderValidationResult(validation) {
    let resultHTML = '';

    // 主要验证结果
    resultHTML += `
        <div class="validation-item ${validation.is_valid ? 'validation-success' : 'validation-error'}">
            <i class="fas ${validation.is_valid ? 'fa-check-circle' : 'fa-times-circle'}"></i>
            ${validation.is_valid ? '排班验证通过' : '排班验证失败'}
        </div>
    `;

    // 错误信息
    validation.errors.forEach(error => {
        resultHTML += `
            <div class="validation-item validation-error">
                <i class="fas fa-times-circle"></i>
                ${error}
            </div>
        `;
    });

    // 警告信息
    validation.warnings.forEach(warning => {
        resultHTML += `
            <div class="validation-item validation-warning">
                <i class="fas fa-exclamation-triangle"></i>
                ${warning}
            </div>
        `;
    });

    elements.validationResult.innerHTML = resultHTML;
}

// 渲染统计表格
function renderStatsTable(stats) {
    if (stats.length === 0) {
        elements.statsTable.innerHTML = '<div class="empty-state">暂无统计数据</div>';
        return;
    }

    let tableHTML = `
        <table>
            <thead>
                <tr>
                    <th>员工姓名</th>
                    <th>早班(16:30)</th>
                    <th>晚班(20:30)</th>
                    <th>休息天数</th>
                    <th>周日休息</th>
                </tr>
            </thead>
            <tbody>
    `;

    stats.forEach(stat => {
        tableHTML += `
            <tr>
                <td>${stat.employee_name}</td>
                <td><span class="stats-badge early">${stat.early_shifts}</span></td>
                <td><span class="stats-badge late">${stat.late_shifts}</span></td>
                <td><span class="stats-badge off">${stat.off_days}</span></td>
                <td>
                    <i class="fas ${stat.sunday_off ? 'fa-check-circle' : 'fa-times-circle'}"
                       style="color: ${stat.sunday_off ? '#27ae60' : '#e74c3c'}"></i>
                </td>
            </tr>
        `;
    });

    tableHTML += '</tbody></table>';

    // 添加汇总信息
    const totalEarly = stats.reduce((sum, stat) => sum + stat.early_shifts, 0);
    const totalLate = stats.reduce((sum, stat) => sum + stat.late_shifts, 0);
    const sundayOffRate = Math.round((stats.filter(s => s.sunday_off).length / stats.length) * 100);

    tableHTML += `
        <div style="margin-top: 2rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div style="background: #e3f2fd; padding: 1rem; border-radius: 6px; text-align: center;">
                <div style="color: #1976d2; font-size: 0.9rem; margin-bottom: 0.5rem;">总早班数</div>
                <div style="color: #1976d2; font-size: 1.5rem; font-weight: bold;">${totalEarly}</div>
            </div>
            <div style="background: #fff3e0; padding: 1rem; border-radius: 6px; text-align: center;">
                <div style="color: #f57c00; font-size: 0.9rem; margin-bottom: 0.5rem;">总晚班数</div>
                <div style="color: #f57c00; font-size: 1.5rem; font-weight: bold;">${totalLate}</div>
            </div>
            <div style="background: #e8f5e8; padding: 1rem; border-radius: 6px; text-align: center;">
                <div style="color: #27ae60; font-size: 0.9rem; margin-bottom: 0.5rem;">周日休息率</div>
                <div style="color: #27ae60; font-size: 1.5rem; font-weight: bold;">${sundayOffRate}%</div>
            </div>
        </div>
    `;

    elements.statsTable.innerHTML = tableHTML;
}

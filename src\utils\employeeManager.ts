import { Employee } from '../types';

// 生成唯一ID
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 创建新员工
export function createEmployee(name: string): Employee {
  return {
    id: generateId(),
    name: name.trim(),
    isActive: true
  };
}

// 验证员工姓名
export function validateEmployeeName(name: string, existingEmployees: Employee[], excludeId?: string): string[] {
  const errors: string[] = [];
  
  if (!name || name.trim().length === 0) {
    errors.push('员工姓名不能为空');
  }
  
  if (name.trim().length > 20) {
    errors.push('员工姓名不能超过20个字符');
  }
  
  const trimmedName = name.trim();
  const isDuplicate = existingEmployees.some(emp => 
    emp.name === trimmedName && emp.id !== excludeId
  );
  
  if (isDuplicate) {
    errors.push('员工姓名已存在');
  }
  
  return errors;
}

// 默认员工数据
export function getDefaultEmployees(): Employee[] {
  return [
    { id: 'emp1', name: '张三', isActive: true },
    { id: 'emp2', name: '李四', isActive: true },
    { id: 'emp3', name: '王五', isActive: true },
    { id: 'emp4', name: '赵六', isActive: true },
    { id: 'emp5', name: '钱七', isActive: true },
    { id: 'emp6', name: '孙八', isActive: true }
  ];
}

// 本地存储键名
const STORAGE_KEY = 'paiban_employees';

// 保存员工数据到本地存储
export function saveEmployeesToStorage(employees: Employee[]): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(employees));
  } catch (error) {
    console.error('保存员工数据失败:', error);
  }
}

// 从本地存储加载员工数据
export function loadEmployeesFromStorage(): Employee[] {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const employees = JSON.parse(stored);
      // 验证数据格式
      if (Array.isArray(employees) && employees.every(emp => 
        emp.id && emp.name && typeof emp.isActive === 'boolean'
      )) {
        return employees;
      }
    }
  } catch (error) {
    console.error('加载员工数据失败:', error);
  }
  
  // 如果没有有效数据，返回默认数据
  return getDefaultEmployees();
}

# 三岗位排班系统

一个专为电流线、电压线、资料组三个岗位设计的智能排班系统，支持特殊的工作日和休息日安排。

## 🎯 系统特点

### ✨ 岗位配置
- **电流线**：6人（丁磊已调至资料组）
- **电压线**：5人
- **资料组**：5人（包含丁磊）

### 📅 工作安排
- **工作日**：周一、周六（共2天）
- **休息日**：周二、周三、周四、周五、周日（共5天）

### 📋 排班规则
1. ✅ 每个员工一周内必须安排至少1天休息
2. ✅ 电流组在周日可以有2个人休息
3. ✅ 电压线和资料组在任意休息日最多只能有1个人休息
4. ✅ 工作日确保三个岗位都有足够人员值班
5. ✅ 支持手动调整排班安排

## 🚀 使用方法

### 快速开始
1. 双击 `schedule_shifts.html` 文件在浏览器中打开
2. 系统会自动加载默认员工数据
3. 点击"生成排班"创建本周排班表

### 功能操作

#### 1. 岗位管理
- **查看员工**：在"岗位管理"页面查看各岗位人员
- **添加员工**：点击"添加员工"按钮，选择岗位和状态
- **编辑员工**：点击员工行的编辑按钮修改信息
- **启用/停用**：点击状态按钮切换员工工作状态
- **删除员工**：点击删除按钮移除员工（需确认）

#### 2. 排班管理
- **切换周次**：使用"上周/下周"按钮切换不同周次
- **生成排班**：点击"生成排班"按钮自动创建排班表
- **手动调整**：在排班表中使用下拉框调整个人排班
- **实时验证**：系统会自动检查排班规则并显示提示

#### 3. 统计分析
- **总体统计**：查看员工总数、工作日、休息日等统计
- **个人详情**：查看每个员工的具体工作和休息安排
- **休息率分析**：了解各员工的休息时间分配

## 🔧 技术特性

### 纯前端实现
- **零依赖**：无需安装任何软件或服务器
- **即开即用**：双击HTML文件即可使用
- **本地存储**：数据保存在浏览器localStorage中
- **离线可用**：无需网络连接

### 智能算法
- **轮换休息**：自动轮换安排员工休息日
- **规则验证**：实时检查排班是否符合规则
- **冲突提示**：自动提示排班冲突和注意事项

### 响应式设计
- **多设备支持**：支持电脑、平板、手机访问
- **现代界面**：采用现代化UI设计
- **操作便捷**：直观的用户交互体验

## 📊 排班规则详解

### 工作日安排（周一、周六）
- 所有启用状态的员工都需要上班
- 三个岗位都必须有人值班
- 不允许在工作日安排休息

### 休息日安排（周二-周五、周日）
- **周日特殊规则**：电流组可以2人休息，其他组各1人休息
- **其他休息日**：每个岗位最多1人休息
- **轮换机制**：系统自动轮换休息人员，确保公平

### 员工要求
- 每个员工一周内至少有1天休息
- 停用状态的员工不参与排班
- 支持临时调整个人排班安排

## 💾 数据管理

### 本地存储
- 员工数据自动保存到浏览器localStorage
- 排班数据实时保存，刷新页面不丢失
- 清除浏览器数据会重置为默认设置

### 默认数据
系统预设了16名员工的默认数据：
- 电流线：张三、李四、王五、赵六、孙七、周八
- 电压线：吴九、郑十、王十一、李十二、张十三
- 资料组：陈十四、刘十五、黄十六、林十七、丁磊

## 🔄 与原系统对比

| 功能特性 | 三岗位系统 | 原排班系统 |
|---------|-----------|-----------|
| 工作日设置 | 周一、周六 | 周一至周六 |
| 休息日规则 | 分岗位限制 | 周日统一休息 |
| 岗位管理 | 3个固定岗位 | 通用员工管理 |
| 排班算法 | 特殊轮换规则 | 早晚班分配 |
| 适用场景 | 特定工作制度 | 通用排班需求 |

## 🌟 适用场景

### 推荐使用情况
- 采用周一、周六工作制的团队
- 需要按岗位分组管理的组织
- 有特殊休息日安排需求的单位
- 小型团队（20人以下）的排班管理

## 🆘 常见问题

**Q: 可以修改工作日设置吗？**
A: 可以修改HTML文件中的WORK_DAYS和REST_DAYS配置。

**Q: 如何增加岗位？**
A: 需要修改DEPARTMENTS配置并相应调整排班算法。

**Q: 数据会丢失吗？**
A: 数据保存在浏览器本地存储中，除非主动清除否则不会丢失。

**Q: 支持数据导出吗？**
A: 当前版本不支持，如需此功能建议使用后端版本。

## 📄 许可证

MIT License

---

**享受高效便捷的三岗位排班管理！** 🎉

# 三岗位排班系统

一个专为电流线、电压线、资料组三个岗位设计的智能排班系统，支持特殊的工作日和休息日安排，具备跨周轮换、多周数据管理和导出功能。

## 🎯 系统特点

### ✨ 岗位配置
- **电流线**：6人（丁磊已调至资料组）
- **电压线**：5人
- **资料组**：5人（包含丁磊）

### 📅 工作安排
- **工作日**：周一、周六（共2天）
- **休息日**：周二、周三、周四、周五、周日（共5天）

### 📋 排班规则
1. ✅ 每个员工一周内必须安排至少1天休息
2. ✅ 电流组在周日可以有2个人休息
3. ✅ 电压线和资料组在任意休息日最多只能有1个人休息
4. ✅ 工作日确保三个岗位都有足够人员值班
5. ✅ 支持手动调整排班安排

### 🔄 跨周轮换
- **智能轮换**：员工休息日在不同周次间自动轮换
- **公平分配**：避免某些员工总是获得"更好"的休息日
- **历史记录**：保留最近10周的排班历史用于轮换分析

### 💾 多周数据管理
- **独立存储**：每周排班数据独立保存，互不影响
- **历史查看**：可以查看和编辑任意已生成的周次排班
- **周导航**：快速跳转到任意已保存的周次

### 📤 导出功能 🆕
- **图片导出**：将排班表导出为高清PNG图片
- **PDF导出**：生成专业格式的PDF排班表
- **格式优化**：导出内容包含完整的排班信息和规则说明

## 🚀 使用方法

### 快速开始
1. 双击 `schedule_shifts.html` 文件在浏览器中打开
2. 系统会自动加载默认员工数据
3. 点击"生成排班"创建本周排班表

### 功能操作

#### 1. 岗位管理
- **查看员工**：在"岗位管理"页面查看各岗位人员
- **添加员工**：点击"添加员工"按钮，选择岗位和状态
- **编辑员工**：点击员工行的编辑按钮修改信息
- **启用/停用**：点击状态按钮切换员工工作状态
- **删除员工**：点击删除按钮移除员工（需确认）

#### 2. 排班管理
- **切换周次**：使用"上周/下周"按钮切换不同周次
- **生成排班**：点击"生成排班"按钮自动创建排班表
- **手动调整**：在排班表中使用下拉框调整个人排班
- **实时验证**：系统会自动检查排班规则并显示提示
- **周导航**：点击"周导航"查看所有已保存的周次

#### 3. 轮换信息
- **轮换对比**：查看员工在不同周次的休息日变化
- **轮换统计**：了解轮换率和分配公平性
- **历史分析**：基于历史数据的轮换效果分析

#### 4. 导出功能 🆕
- **导出图片**：点击"导出" → "导出为图片"，生成PNG格式排班表
- **导出PDF**：点击"导出" → "导出为PDF"，生成专业PDF文档
- **自动命名**：导出文件自动以"排班表_YYYY-MM-DD"格式命名
- **完整信息**：包含排班表、周次信息、生成时间和规则说明

#### 5. 统计分析
- **总体统计**：查看员工总数、工作日、休息日等统计
- **个人详情**：查看每个员工的具体工作和休息安排
- **休息率分析**：了解各员工的休息时间分配

#### 6. 数据管理
- **导出数据**：将员工和排班数据导出为JSON文件进行备份
- **导入数据**：从JSON文件恢复之前的数据
- **数据预览**：导入前可预览文件内容确认数据正确性
- **格式验证**：自动验证导入文件的格式和数据完整性

## 🔧 技术特性

### 纯前端实现
- **零依赖**：无需安装任何软件或服务器
- **即开即用**：双击HTML文件即可使用
- **本地存储**：数据保存在浏览器localStorage中
- **离线可用**：无需网络连接
- **导出支持**：集成html2canvas和jsPDF库实现导出功能

### 智能算法
- **跨周轮换**：自动轮换安排员工休息日，确保公平性
- **规则验证**：实时检查排班是否符合规则
- **冲突提示**：自动提示排班冲突和注意事项
- **历史分析**：基于历史数据优化排班分配

### 响应式设计
- **多设备支持**：支持电脑、平板、手机访问
- **现代界面**：采用现代化UI设计
- **操作便捷**：直观的用户交互体验

## 📊 排班规则详解

### 工作日安排（周一、周六）
- 所有启用状态的员工都需要上班
- 三个岗位都必须有人值班
- 不允许在工作日安排休息

### 休息日安排（周二-周五、周日）
- **周日特殊规则**：电流组可以2人休息，其他组各1人休息
- **其他休息日**：每个岗位最多1人休息
- **轮换机制**：系统自动轮换休息人员，确保公平

### 员工要求
- 每个员工一周内至少有1天休息
- 停用状态的员工不参与排班
- 支持临时调整个人排班安排

## 💾 数据管理

### 多周数据存储
- **按周存储**：每周排班数据独立保存，使用周一日期作为键值
- **历史保持**：切换周次时自动加载对应周的排班数据
- **索引管理**：维护周次索引，支持快速查找和导航

### 导入导出功能
- **JSON数据**：支持完整的员工和排班数据导入导出
- **图片导出**：高清PNG格式，适合打印和分享
- **PDF导出**：专业文档格式，包含完整排班信息
- **自动备份**：建议定期导出数据作为备份

### 默认数据
系统预设了16名员工的默认数据：
- 电流线：张三、李四、王五、赵六、孙七、周八
- 电压线：吴九、郑十、王十一、李十二、张十三
- 资料组：陈十四、刘十五、黄十六、林十七、丁磊

## 🌟 适用场景

### 推荐使用情况
- 采用周一、周六工作制的团队
- 需要按岗位分组管理的组织
- 有特殊休息日安排需求的单位
- 需要定期导出排班表的管理场景
- 小型团队（20人以下）的排班管理

## 🆘 常见问题

**Q: 如何导出排班表？**
A: 生成排班后，点击"导出"按钮，选择"导出为图片"或"导出为PDF"即可。

**Q: 导出的文件保存在哪里？**
A: 文件会保存到浏览器的默认下载文件夹，文件名格式为"排班表_YYYY-MM-DD"。

**Q: 可以修改工作日设置吗？**
A: 可以修改HTML文件中的WORK_DAYS和REST_DAYS配置。

**Q: 如何在不同设备间同步数据？**
A: 使用数据管理页面的导出功能将数据保存为JSON文件，然后在其他设备上导入该文件即可。

**Q: 数据会丢失吗？**
A: 数据保存在浏览器本地存储中，现在支持多周独立存储和导出备份功能。

**Q: 导出功能需要网络连接吗？**
A: 首次加载页面时需要网络连接来加载导出库，之后可以离线使用导出功能。

## 📄 许可证

MIT License

---

**享受高效便捷的三岗位排班管理！** 🎉

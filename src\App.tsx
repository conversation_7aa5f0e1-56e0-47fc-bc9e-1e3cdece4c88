import React, { useState, useEffect } from 'react';
import { Calendar, RefreshCw, Users, BarChart3 } from 'lucide-react';
import { startOfWeek, addWeeks, subWeeks } from 'date-fns';
import { Employee, WeekSchedule, ShiftType } from './types';
import { EmployeeManager } from './components/EmployeeManager';
import { ScheduleTable } from './components/ScheduleTable';
import { ScheduleStatsComponent } from './components/ScheduleStats';
import { generateAutoSchedule, calculateScheduleStats, validateSchedule } from './utils/scheduleAlgorithm';
import { loadEmployeesFromStorage, saveEmployeesToStorage } from './utils/employeeManager';

function App() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [currentWeek, setCurrentWeek] = useState(() => startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [schedule, setSchedule] = useState<WeekSchedule | null>(null);
  const [activeTab, setActiveTab] = useState<'employees' | 'schedule' | 'stats'>('employees');

  // 加载员工数据
  useEffect(() => {
    const loadedEmployees = loadEmployeesFromStorage();
    setEmployees(loadedEmployees);
  }, []);

  // 保存员工数据
  useEffect(() => {
    saveEmployeesToStorage(employees);
  }, [employees]);

  const handleEmployeesChange = (newEmployees: Employee[]) => {
    setEmployees(newEmployees);
    // 如果有排班表，重新生成以反映员工变化
    if (schedule) {
      const newSchedule = generateAutoSchedule(newEmployees, currentWeek);
      setSchedule(newSchedule);
    }
  };

  const handleGenerateSchedule = () => {
    const newSchedule = generateAutoSchedule(employees, currentWeek);
    setSchedule(newSchedule);
    setActiveTab('schedule');
  };

  const handleShiftChange = (employeeId: string, dayIndex: number, shift: ShiftType) => {
    if (!schedule) return;

    const newSchedule = { ...schedule };
    newSchedule.days = [...schedule.days];
    newSchedule.days[dayIndex] = {
      ...schedule.days[dayIndex],
      assignments: {
        ...schedule.days[dayIndex].assignments,
        [employeeId]: shift
      }
    };
    setSchedule(newSchedule);
  };

  const handleWeekChange = (direction: 'prev' | 'next') => {
    const newWeek = direction === 'prev'
      ? subWeeks(currentWeek, 1)
      : addWeeks(currentWeek, 1);
    setCurrentWeek(newWeek);

    // 如果有排班表，为新周生成排班
    if (schedule) {
      const newSchedule = generateAutoSchedule(employees, newWeek);
      setSchedule(newSchedule);
    }
  };

  const stats = schedule ? calculateScheduleStats(schedule, employees) : [];
  const validation = schedule ? validateSchedule(schedule, employees) : { isValid: false, errors: [], warnings: [] };

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">智能排班系统</h1>
            <div className="flex items-center gap-4">
              <button
                onClick={handleGenerateSchedule}
                disabled={employees.filter(emp => emp.isActive).length === 0}
                className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <RefreshCw size={16} />
                生成排班
              </button>
            </div>
          </div>
        </div>
      </header>

      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('employees')}
              className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'employees'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Users size={16} />
              员工管理
            </button>
            <button
              onClick={() => setActiveTab('schedule')}
              className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'schedule'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Calendar size={16} />
              排班表
            </button>
            <button
              onClick={() => setActiveTab('stats')}
              className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'stats'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <BarChart3 size={16} />
              统计分析
            </button>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'employees' && (
          <EmployeeManager
            employees={employees}
            onEmployeesChange={handleEmployeesChange}
          />
        )}

        {activeTab === 'schedule' && (
          <div className="space-y-6">
            {/* 周选择器 */}
            <div className="bg-white rounded-lg shadow-md p-4">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => handleWeekChange('prev')}
                  className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
                >
                  ← 上一周
                </button>
                <h3 className="text-lg font-medium text-gray-800">
                  {currentWeek.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })} 周
                </h3>
                <button
                  onClick={() => handleWeekChange('next')}
                  className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
                >
                  下一周 →
                </button>
              </div>
            </div>

            <ScheduleTable
              schedule={schedule}
              employees={employees}
              onShiftChange={handleShiftChange}
            />
          </div>
        )}

        {activeTab === 'stats' && (
          <ScheduleStatsComponent
            stats={stats}
            validation={validation}
          />
        )}
      </main>
    </div>
  );
}

export default App;

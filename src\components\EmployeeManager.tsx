import React, { useState } from 'react';
import { Plus, Edit2, Trash2, Use<PERSON><PERSON><PERSON><PERSON>, UserX } from 'lucide-react';
import { Employee } from '../types';
import { createEmployee, validateEmployeeName } from '../utils/employeeManager';

interface EmployeeManagerProps {
  employees: Employee[];
  onEmployeesChange: (employees: Employee[]) => void;
}

export function EmployeeManager({ employees, onEmployeesChange }: EmployeeManagerProps) {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newName, setNewName] = useState('');
  const [errors, setErrors] = useState<string[]>([]);

  const handleAddEmployee = () => {
    const validationErrors = validateEmployeeName(newName, employees);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    const newEmployee = createEmployee(newName);
    onEmployeesChange([...employees, newEmployee]);
    setNewName('');
    setIsAdding(false);
    setErrors([]);
  };

  const handleEditEmployee = (id: string) => {
    const validationErrors = validateEmployeeName(newName, employees, id);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    const updatedEmployees = employees.map(emp =>
      emp.id === id ? { ...emp, name: newName.trim() } : emp
    );
    onEmployeesChange(updatedEmployees);
    setEditingId(null);
    setNewName('');
    setErrors([]);
  };

  const handleDeleteEmployee = (id: string) => {
    if (confirm('确定要删除这个员工吗？')) {
      const updatedEmployees = employees.filter(emp => emp.id !== id);
      onEmployeesChange(updatedEmployees);
    }
  };

  const handleToggleActive = (id: string) => {
    const updatedEmployees = employees.map(emp =>
      emp.id === id ? { ...emp, isActive: !emp.isActive } : emp
    );
    onEmployeesChange(updatedEmployees);
  };

  const startEdit = (employee: Employee) => {
    setEditingId(employee.id);
    setNewName(employee.name);
    setErrors([]);
  };

  const cancelEdit = () => {
    setEditingId(null);
    setIsAdding(false);
    setNewName('');
    setErrors([]);
  };

  const startAdd = () => {
    setIsAdding(true);
    setNewName('');
    setErrors([]);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">员工管理</h2>
        {!isAdding && !editingId && (
          <button
            onClick={startAdd}
            className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors"
          >
            <Plus size={16} />
            添加员工
          </button>
        )}
      </div>

      {/* 错误提示 */}
      {errors.length > 0 && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          {errors.map((error, index) => (
            <p key={index} className="text-red-600 text-sm">{error}</p>
          ))}
        </div>
      )}

      {/* 添加员工表单 */}
      {isAdding && (
        <div className="mb-4 p-4 bg-gray-50 rounded-md">
          <div className="flex gap-2">
            <input
              type="text"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              placeholder="输入员工姓名"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleAddEmployee()}
              autoFocus
            />
            <button
              onClick={handleAddEmployee}
              className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 transition-colors"
            >
              确定
            </button>
            <button
              onClick={cancelEdit}
              className="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors"
            >
              取消
            </button>
          </div>
        </div>
      )}

      {/* 员工列表 */}
      <div className="space-y-2">
        {employees.map((employee) => (
          <div
            key={employee.id}
            className={`flex items-center justify-between p-3 rounded-md border ${
              employee.isActive ? 'bg-white border-gray-200' : 'bg-gray-50 border-gray-300'
            }`}
          >
            {editingId === employee.id ? (
              <div className="flex gap-2 flex-1">
                <input
                  type="text"
                  value={newName}
                  onChange={(e) => setNewName(e.target.value)}
                  className="flex-1 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  onKeyPress={(e) => e.key === 'Enter' && handleEditEmployee(employee.id)}
                  autoFocus
                />
                <button
                  onClick={() => handleEditEmployee(employee.id)}
                  className="bg-green-500 text-white px-3 py-1 rounded-md hover:bg-green-600 transition-colors"
                >
                  确定
                </button>
                <button
                  onClick={cancelEdit}
                  className="bg-gray-500 text-white px-3 py-1 rounded-md hover:bg-gray-600 transition-colors"
                >
                  取消
                </button>
              </div>
            ) : (
              <>
                <div className="flex items-center gap-3">
                  <span className={`font-medium ${employee.isActive ? 'text-gray-800' : 'text-gray-500'}`}>
                    {employee.name}
                  </span>
                  {!employee.isActive && (
                    <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                      已停用
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleToggleActive(employee.id)}
                    className={`p-1 rounded-md transition-colors ${
                      employee.isActive
                        ? 'text-green-600 hover:bg-green-50'
                        : 'text-gray-400 hover:bg-gray-100'
                    }`}
                    title={employee.isActive ? '停用员工' : '启用员工'}
                  >
                    {employee.isActive ? <UserCheck size={16} /> : <UserX size={16} />}
                  </button>
                  <button
                    onClick={() => startEdit(employee)}
                    className="p-1 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                    title="编辑员工"
                  >
                    <Edit2 size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteEmployee(employee.id)}
                    className="p-1 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    title="删除员工"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </>
            )}
          </div>
        ))}
      </div>

      {employees.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          暂无员工，请添加员工后开始排班
        </div>
      )}
    </div>
  );
}

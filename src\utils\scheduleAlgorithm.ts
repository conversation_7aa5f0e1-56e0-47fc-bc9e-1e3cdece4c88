import { startOfWeek, addDays, format } from 'date-fns';
import { Employee, WeekSchedule, DaySchedule, ShiftType, WeekDay, ScheduleStats, ValidationResult } from '../types';

// 获取星期几的映射
const getWeekDay = (date: Date): WeekDay => {
  const days: WeekDay[] = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  return days[date.getDay()];
};

// 生成自动排班
export function generateAutoSchedule(employees: Employee[], weekStartDate: Date): WeekSchedule {
  const activeEmployees = employees.filter(emp => emp.isActive);
  const weekStart = startOfWeek(weekStartDate, { weekStartsOn: 1 }); // 周一开始
  
  // 创建7天的排班
  const days: DaySchedule[] = [];
  
  for (let i = 0; i < 7; i++) {
    const date = addDays(weekStart, i);
    const weekDay = getWeekDay(date);
    const assignments: Record<string, ShiftType> = {};
    
    if (weekDay === 'sunday') {
      // 周日所有人休息
      activeEmployees.forEach(emp => {
        assignments[emp.id] = 'off';
      });
    } else {
      // 工作日分配班次
      const shuffledEmployees = [...activeEmployees].sort(() => Math.random() - 0.5);
      const halfCount = Math.ceil(shuffledEmployees.length / 2);
      
      shuffledEmployees.forEach((emp, index) => {
        // 前一半分配早班，后一半分配晚班
        assignments[emp.id] = index < halfCount ? 'early' : 'late';
      });
    }
    
    days.push({
      date,
      weekDay,
      assignments
    });
  }
  
  // 优化分配，确保每人早晚班尽量平均
  optimizeShiftDistribution(days, activeEmployees);
  
  return {
    weekStart,
    days
  };
}

// 优化班次分配，确保早晚班平均分配
function optimizeShiftDistribution(days: DaySchedule[], employees: Employee[]) {
  const workDays = days.filter(day => day.weekDay !== 'sunday');
  const employeeShiftCounts = new Map<string, { early: number; late: number }>();
  
  // 初始化计数
  employees.forEach(emp => {
    employeeShiftCounts.set(emp.id, { early: 0, late: 0 });
  });
  
  // 统计当前分配
  workDays.forEach(day => {
    Object.entries(day.assignments).forEach(([empId, shift]) => {
      const counts = employeeShiftCounts.get(empId);
      if (counts && shift !== 'off') {
        counts[shift]++;
      }
    });
  });
  
  // 尝试平衡分配
  for (let iteration = 0; iteration < 10; iteration++) {
    let swapped = false;
    
    workDays.forEach(day => {
      const assignments = Object.entries(day.assignments);
      
      for (let i = 0; i < assignments.length - 1; i++) {
        for (let j = i + 1; j < assignments.length; j++) {
          const [empId1, shift1] = assignments[i];
          const [empId2, shift2] = assignments[j];
          
          if (shift1 !== 'off' && shift2 !== 'off' && shift1 !== shift2) {
            const counts1 = employeeShiftCounts.get(empId1)!;
            const counts2 = employeeShiftCounts.get(empId2)!;
            
            // 计算交换后的平衡度
            const currentImbalance = Math.abs(counts1.early - counts1.late) + Math.abs(counts2.early - counts2.late);
            
            const newCounts1 = { ...counts1 };
            const newCounts2 = { ...counts2 };
            
            newCounts1[shift1]--;
            newCounts1[shift2]++;
            newCounts2[shift2]--;
            newCounts2[shift1]++;
            
            const newImbalance = Math.abs(newCounts1.early - newCounts1.late) + Math.abs(newCounts2.early - newCounts2.late);
            
            // 如果交换能改善平衡度，则交换
            if (newImbalance < currentImbalance) {
              day.assignments[empId1] = shift2;
              day.assignments[empId2] = shift1;
              employeeShiftCounts.set(empId1, newCounts1);
              employeeShiftCounts.set(empId2, newCounts2);
              swapped = true;
            }
          }
        }
      }
    });
    
    if (!swapped) break;
  }
}

// 计算排班统计
export function calculateScheduleStats(schedule: WeekSchedule, employees: Employee[]): ScheduleStats[] {
  return employees.map(emp => {
    let earlyShifts = 0;
    let lateShifts = 0;
    let offDays = 0;
    let sundayOff = false;
    
    schedule.days.forEach(day => {
      const shift = day.assignments[emp.id];
      if (shift === 'early') earlyShifts++;
      else if (shift === 'late') lateShifts++;
      else if (shift === 'off') {
        offDays++;
        if (day.weekDay === 'sunday') sundayOff = true;
      }
    });
    
    return {
      employeeId: emp.id,
      employeeName: emp.name,
      earlyShifts,
      lateShifts,
      offDays,
      sundayOff
    };
  });
}

// 验证排班是否符合规则
export function validateSchedule(schedule: WeekSchedule, employees: Employee[]): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const stats = calculateScheduleStats(schedule, employees);
  
  // 检查周日是否都休息
  stats.forEach(stat => {
    if (!stat.sundayOff) {
      errors.push(`${stat.employeeName} 周日没有休息`);
    }
  });
  
  // 检查早晚班分配是否平均
  const avgEarly = stats.reduce((sum, stat) => sum + stat.earlyShifts, 0) / stats.length;
  const avgLate = stats.reduce((sum, stat) => sum + stat.lateShifts, 0) / stats.length;
  
  stats.forEach(stat => {
    const earlyDiff = Math.abs(stat.earlyShifts - avgEarly);
    const lateDiff = Math.abs(stat.lateShifts - avgLate);
    
    if (earlyDiff > 1.5) {
      warnings.push(`${stat.employeeName} 早班分配不均衡 (${stat.earlyShifts}班，平均${avgEarly.toFixed(1)}班)`);
    }
    
    if (lateDiff > 1.5) {
      warnings.push(`${stat.employeeName} 晚班分配不均衡 (${stat.lateShifts}班，平均${avgLate.toFixed(1)}班)`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

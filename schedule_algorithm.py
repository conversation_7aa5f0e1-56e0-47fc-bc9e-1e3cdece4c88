from datetime import datetime, timedelta
import random
from typing import List, Dict, Any
from dateutil.relativedelta import relativedelta

class ScheduleAlgorithm:
    """排班算法类"""
    
    SHIFT_TYPES = {
        'early': '早班(16:30)',
        'late': '晚班(20:30)', 
        'off': '休息'
    }
    
    WEEKDAYS = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    
    def __init__(self):
        pass
    
    def generate_schedule(self, employees: List[Dict], week_start: datetime) -> Dict[str, Any]:
        """生成自动排班表"""
        # 确保周开始是周一
        week_start = week_start - timedelta(days=week_start.weekday())
        
        # 创建7天的排班
        days = []
        for i in range(7):
            date = week_start + timedelta(days=i)
            weekday = self.WEEKDAYS[date.weekday()]
            
            assignments = {}
            
            if weekday == 'sunday':
                # 周日所有人休息
                for emp in employees:
                    assignments[emp['id']] = 'off'
            else:
                # 工作日分配班次
                active_employees = [emp for emp in employees if emp.get('is_active', True)]
                shuffled_employees = active_employees.copy()
                random.shuffle(shuffled_employees)
                
                half_count = len(shuffled_employees) // 2
                
                for idx, emp in enumerate(shuffled_employees):
                    # 前一半分配早班，后一半分配晚班
                    if idx < half_count:
                        assignments[emp['id']] = 'early'
                    else:
                        assignments[emp['id']] = 'late'
            
            days.append({
                'date': date.isoformat(),
                'weekday': weekday,
                'assignments': assignments
            })
        
        schedule = {
            'week_start': week_start.isoformat(),
            'days': days
        }
        
        # 优化班次分配
        self._optimize_shift_distribution(schedule, employees)
        
        return schedule
    
    def _optimize_shift_distribution(self, schedule: Dict, employees: List[Dict]) -> None:
        """优化班次分配，确保早晚班尽量平均"""
        work_days = [day for day in schedule['days'] if day['weekday'] != 'sunday']
        
        # 统计每个员工的班次数量
        employee_shift_counts = {}
        for emp in employees:
            employee_shift_counts[emp['id']] = {'early': 0, 'late': 0}
        
        # 计算当前分配
        for day in work_days:
            for emp_id, shift in day['assignments'].items():
                if shift in ['early', 'late']:
                    employee_shift_counts[emp_id][shift] += 1
        
        # 尝试平衡分配（简单的交换算法）
        for iteration in range(10):
            swapped = False
            
            for day in work_days:
                assignments = list(day['assignments'].items())
                
                for i in range(len(assignments) - 1):
                    for j in range(i + 1, len(assignments)):
                        emp_id1, shift1 = assignments[i]
                        emp_id2, shift2 = assignments[j]
                        
                        if shift1 != 'off' and shift2 != 'off' and shift1 != shift2:
                            counts1 = employee_shift_counts[emp_id1]
                            counts2 = employee_shift_counts[emp_id2]
                            
                            # 计算当前不平衡度
                            current_imbalance = abs(counts1['early'] - counts1['late']) + abs(counts2['early'] - counts2['late'])
                            
                            # 计算交换后的不平衡度
                            new_counts1 = counts1.copy()
                            new_counts2 = counts2.copy()
                            
                            new_counts1[shift1] -= 1
                            new_counts1[shift2] += 1
                            new_counts2[shift2] -= 1
                            new_counts2[shift1] += 1
                            
                            new_imbalance = abs(new_counts1['early'] - new_counts1['late']) + abs(new_counts2['early'] - new_counts2['late'])
                            
                            # 如果交换能改善平衡度，则交换
                            if new_imbalance < current_imbalance:
                                day['assignments'][emp_id1] = shift2
                                day['assignments'][emp_id2] = shift1
                                employee_shift_counts[emp_id1] = new_counts1
                                employee_shift_counts[emp_id2] = new_counts2
                                swapped = True
            
            if not swapped:
                break
    
    def validate_schedule(self, schedule: Dict, employees: List[Dict]) -> Dict[str, Any]:
        """验证排班表是否符合规则"""
        errors = []
        warnings = []
        
        stats = self.calculate_stats(schedule, employees)
        
        # 检查周日是否都休息
        for stat in stats:
            if not stat['sunday_off']:
                errors.append(f"{stat['employee_name']} 周日没有休息")
        
        # 检查早晚班分配是否平均
        if stats:
            avg_early = sum(stat['early_shifts'] for stat in stats) / len(stats)
            avg_late = sum(stat['late_shifts'] for stat in stats) / len(stats)
            
            for stat in stats:
                early_diff = abs(stat['early_shifts'] - avg_early)
                late_diff = abs(stat['late_shifts'] - avg_late)
                
                if early_diff > 1.5:
                    warnings.append(f"{stat['employee_name']} 早班分配不均衡 ({stat['early_shifts']}班，平均{avg_early:.1f}班)")
                
                if late_diff > 1.5:
                    warnings.append(f"{stat['employee_name']} 晚班分配不均衡 ({stat['late_shifts']}班，平均{avg_late:.1f}班)")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def calculate_stats(self, schedule: Dict, employees: List[Dict]) -> List[Dict[str, Any]]:
        """计算排班统计"""
        employee_dict = {emp['id']: emp for emp in employees}
        stats = []
        
        for emp in employees:
            early_shifts = 0
            late_shifts = 0
            off_days = 0
            sunday_off = False
            
            for day in schedule['days']:
                shift = day['assignments'].get(emp['id'], 'off')
                
                if shift == 'early':
                    early_shifts += 1
                elif shift == 'late':
                    late_shifts += 1
                elif shift == 'off':
                    off_days += 1
                    if day['weekday'] == 'sunday':
                        sunday_off = True
            
            stats.append({
                'employee_id': emp['id'],
                'employee_name': emp['name'],
                'early_shifts': early_shifts,
                'late_shifts': late_shifts,
                'off_days': off_days,
                'sunday_off': sunday_off
            })
        
        return stats

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能排班系统 - 纯前端版</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        .header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e0e0e0;
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 20px;
        }

        .title {
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .title i {
            margin-right: 0.5rem;
            color: #3498db;
        }

        /* 导航栏样式 */
        .nav {
            background: #fff;
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-tabs {
            display: flex;
            gap: 0;
        }

        .nav-tab {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            color: #666;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-tab:hover {
            color: #3498db;
            background-color: #f8f9fa;
        }

        .nav-tab.active {
            color: #3498db;
            border-bottom-color: #3498db;
            background-color: #f8f9fa;
        }

        /* 主内容区样式 */
        .main {
            padding: 2rem 0;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 卡片样式 */
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            color: #2c3e50;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* 按钮样式 */
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .btn-warning {
            background-color: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background-color: #e67e22;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        /* 员工列表样式 */
        .employees-list {
            display: grid;
            gap: 1rem;
        }

        .employee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #f8f9fa;
        }

        .employee-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .employee-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .employee-status {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .employee-status.active {
            background-color: #d4edda;
            color: #155724;
        }

        .employee-status.inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .employee-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* 周选择器样式 */
        .week-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .current-week {
            font-weight: 500;
            color: #2c3e50;
            min-width: 200px;
            text-align: center;
        }

        /* 排班表样式 */
        .schedule-table {
            overflow-x: auto;
        }

        .schedule-table table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .schedule-table th,
        .schedule-table td {
            border: 1px solid #e0e0e0;
            padding: 0.75rem;
            text-align: center;
        }

        .schedule-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .schedule-table .sunday {
            background-color: #fff5f5;
            color: #e74c3c;
        }

        .shift-select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
        }

        .shift-early {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .shift-late {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .shift-off {
            background-color: #f5f5f5;
            color: #666;
        }

        /* 统计表格样式 */
        .stats-table table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .stats-table th,
        .stats-table td {
            border: 1px solid #e0e0e0;
            padding: 0.75rem;
            text-align: center;
        }

        .stats-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .stats-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .stats-badge.early {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .stats-badge.late {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .stats-badge.off {
            background-color: #f5f5f5;
            color: #666;
        }

        /* 验证结果样式 */
        .validation-result {
            margin-bottom: 2rem;
        }

        .validation-item {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .validation-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .validation-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .validation-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #fff;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .modal-body {
            padding: 1.5rem;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 1.5rem;
        }

        /* 消息样式 */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 6px;
            color: #fff;
            font-weight: 500;
            z-index: 1500;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .message.show {
            transform: translateX(0);
        }

        .message.success {
            background-color: #27ae60;
        }

        .message.error {
            background-color: #e74c3c;
        }

        .message.warning {
            background-color: #f39c12;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #666;
            font-style: italic;
        }

        /* 数据管理样式 */
        .data-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #f8f9fa;
        }

        .data-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .data-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .storage-usage {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .storage-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }

        .storage-item:last-child {
            border-bottom: none;
            font-weight: 600;
            background-color: #f8f9fa;
            margin: 0.5rem -1rem -1rem -1rem;
            padding: 0.75rem 1rem;
        }

        .storage-bar {
            width: 100px;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-left: 1rem;
        }

        .storage-bar-fill {
            height: 100%;
            background-color: #3498db;
            transition: width 0.3s ease;
        }

        .schedule-history {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-info {
            flex: 1;
        }

        .history-date {
            font-weight: 500;
            color: #2c3e50;
        }

        .history-meta {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .history-actions {
            display: flex;
            gap: 0.5rem;
        }

        .text-muted {
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* 拖拽排班样式 */
        .schedule-table td {
            position: relative;
        }

        .shift-select {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .shift-select:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .drop-zone {
            background-color: #e3f2fd !important;
            border: 2px dashed #2196f3 !important;
        }

        .drop-zone::after {
            content: '放置到这里';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #2196f3;
            font-size: 0.8rem;
            pointer-events: none;
        }

        /* 排班模板样式 */
        .template-section {
            margin-bottom: 1.5rem;
        }

        .template-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .template-item {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 1rem;
            background: #fff;
            transition: all 0.2s ease;
        }

        .template-item:hover {
            border-color: #3498db;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .template-name {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .template-description {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 1rem;
        }

        .template-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* 冲突检测样式 */
        .conflict-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            color: #856404;
        }

        .conflict-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            color: #721c24;
        }

        .shift-conflict {
            background-color: #fff3cd !important;
            border-color: #ffc107 !important;
        }

        .shift-error {
            background-color: #f8d7da !important;
            border-color: #dc3545 !important;
        }

        /* 批量操作样式 */
        .batch-operations {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .batch-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .batch-select {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .employee-checkbox {
            margin-right: 0.5rem;
        }

        /* 快速操作按钮 */
        .quick-actions {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .quick-action-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
            font-size: 0.85rem;
            transition: all 0.2s ease;
        }

        .quick-action-btn:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        .quick-action-btn.active {
            background: #3498db;
            color: #fff;
            border-color: #3498db;
        }

        /* 员工技能标签样式 */
        .skill-tags {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-top: 0.5rem;
        }

        .skill-tag {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .skill-tag.manager {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .skill-tag.senior {
            background-color: #e8f5e8;
            color: #27ae60;
        }

        .skill-tag.trainee {
            background-color: #fce4ec;
            color: #c2185b;
        }

        /* 请假管理样式 */
        .leave-section {
            margin-top: 1.5rem;
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #f8f9fa;
        }

        .leave-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            background: #fff;
        }

        .leave-info {
            flex: 1;
        }

        .leave-date {
            font-weight: 500;
            color: #2c3e50;
        }

        .leave-reason {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .leave-status {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .leave-status.approved {
            background-color: #d4edda;
            color: #155724;
        }

        .leave-status.pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .leave-status.rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* 工时统计样式 */
        .work-hours-section {
            margin-top: 1.5rem;
        }

        .hours-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .hours-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 1rem;
            text-align: center;
        }

        .hours-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .hours-label {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.25rem;
        }

        /* 特殊日期样式 */
        .special-dates {
            margin-top: 1rem;
        }

        .special-date-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        .special-date-item:last-child {
            border-bottom: none;
        }

        .holiday-marker {
            background-color: #ffebee;
            color: #c62828;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
        }

        .workday-marker {
            background-color: #e8f5e8;
            color: #2e7d32;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }
            
            .header .container {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-tabs {
                flex-wrap: wrap;
            }
            
            .card-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
            
            .week-selector {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .employee-item {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
            
            .employee-actions {
                width: 100%;
                justify-content: flex-end;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部 -->
        <header class="header">
            <div class="container">
                <h1 class="title">
                    <i class="fas fa-calendar-alt"></i>
                    智能排班系统
                </h1>
                <div class="header-actions">
                    <button id="generateBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        生成排班
                    </button>
                </div>
            </div>
        </header>

        <!-- 导航栏 -->
        <nav class="nav">
            <div class="container">
                <div class="nav-tabs">
                    <button class="nav-tab active" data-tab="employees">
                        <i class="fas fa-users"></i>
                        员工管理
                    </button>
                    <button class="nav-tab" data-tab="schedule">
                        <i class="fas fa-calendar"></i>
                        排班表
                    </button>
                    <button class="nav-tab" data-tab="stats">
                        <i class="fas fa-chart-bar"></i>
                        统计分析
                    </button>
                    <button class="nav-tab" data-tab="data">
                        <i class="fas fa-database"></i>
                        数据管理
                    </button>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <main class="main">
            <div class="container">
                <!-- 员工管理页面 -->
                <div id="employees-tab" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <h2>员工管理</h2>
                            <button id="addEmployeeBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                添加员工
                            </button>
                            <button class="btn btn-secondary" onclick="testLeaveFunction()">
                                <i class="fas fa-bug"></i>
                                测试请假功能
                            </button>
                            <button class="btn btn-info" onclick="forceUpdateSchedule()">
                                <i class="fas fa-sync"></i>
                                强制更新排班表
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- 员工统计 -->
                            <div class="hours-summary">
                                <div class="hours-card">
                                    <div class="hours-value" id="totalEmployees">0</div>
                                    <div class="hours-label">总员工数</div>
                                </div>
                                <div class="hours-card">
                                    <div class="hours-value" id="activeEmployees">0</div>
                                    <div class="hours-label">活跃员工</div>
                                </div>
                                <div class="hours-card">
                                    <div class="hours-value" id="onLeaveEmployees">0</div>
                                    <div class="hours-label">请假员工</div>
                                </div>
                            </div>

                            <div id="employeesList" class="employees-list">
                                <!-- 员工列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 排班表页面 -->
                <div id="schedule-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2>排班表</h2>
                            <div class="week-selector">
                                <button id="prevWeekBtn" class="btn btn-secondary">
                                    <i class="fas fa-chevron-left"></i>
                                    上一周
                                </button>
                                <span id="currentWeek" class="current-week"></span>
                                <button id="nextWeekBtn" class="btn btn-secondary">
                                    下一周
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 快速操作 -->
                            <div class="quick-actions">
                                <button class="quick-action-btn" onclick="toggleDragMode()">
                                    <i class="fas fa-arrows-alt"></i>
                                    <span id="dragModeText">启用拖拽</span>
                                </button>
                                <button class="quick-action-btn" onclick="showTemplateModal()">
                                    <i class="fas fa-save"></i>
                                    保存模板
                                </button>
                                <button class="quick-action-btn" onclick="showLoadTemplateModal()">
                                    <i class="fas fa-folder-open"></i>
                                    加载模板
                                </button>
                                <button class="quick-action-btn" onclick="autoBalance()">
                                    <i class="fas fa-balance-scale"></i>
                                    自动平衡
                                </button>
                                <button class="quick-action-btn" onclick="checkConflicts()">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    检查冲突
                                </button>
                            </div>

                            <!-- 批量操作 -->
                            <div class="batch-operations" id="batchOperations" style="display: none;">
                                <h4><i class="fas fa-tasks"></i> 批量操作</h4>
                                <div class="batch-controls">
                                    <div class="batch-select">
                                        <label>
                                            <input type="checkbox" id="selectAllEmployees" onchange="toggleSelectAllEmployees()">
                                            全选员工
                                        </label>
                                    </div>
                                    <select id="batchShiftType" class="form-control" style="width: auto;">
                                        <option value="early">早班(16:30)</option>
                                        <option value="late">晚班(20:30)</option>
                                    </select>
                                    <select id="batchDayType" class="form-control" style="width: auto;">
                                        <option value="all">所有工作日</option>
                                        <option value="monday">周一</option>
                                        <option value="tuesday">周二</option>
                                        <option value="wednesday">周三</option>
                                        <option value="thursday">周四</option>
                                        <option value="friday">周五</option>
                                        <option value="saturday">周六</option>
                                    </select>
                                    <button class="btn btn-primary" onclick="applyBatchOperation()">
                                        <i class="fas fa-check"></i>
                                        应用
                                    </button>
                                    <button class="btn btn-secondary" onclick="hideBatchOperations()">
                                        <i class="fas fa-times"></i>
                                        取消
                                    </button>
                                </div>
                            </div>

                            <!-- 冲突提示 -->
                            <div id="conflictAlerts" class="conflict-alerts">
                                <!-- 冲突提示将在这里显示 -->
                            </div>

                            <div id="scheduleTable" class="schedule-table">
                                <!-- 排班表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计分析页面 -->
                <div id="stats-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2>统计分析</h2>
                        </div>
                        <div class="card-body">
                            <div id="validationResult" class="validation-result">
                                <!-- 验证结果将在这里显示 -->
                            </div>
                            <div id="statsTable" class="stats-table">
                                <!-- 统计表格将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据管理页面 -->
                <div id="data-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2>数据管理</h2>
                        </div>
                        <div class="card-body">
                            <!-- 数据备份与恢复 -->
                            <div class="data-section">
                                <h3><i class="fas fa-shield-alt"></i> 数据备份与恢复</h3>
                                <div class="data-actions">
                                    <button class="btn btn-primary" onclick="exportData()">
                                        <i class="fas fa-download"></i>
                                        导出数据
                                    </button>
                                    <button class="btn btn-secondary" onclick="document.getElementById('importFile').click()">
                                        <i class="fas fa-upload"></i>
                                        导入数据
                                    </button>
                                    <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(this.files[0])">
                                    <button class="btn btn-warning" onclick="createBackup()">
                                        <i class="fas fa-save"></i>
                                        创建备份
                                    </button>
                                    <button class="btn btn-success" onclick="restoreBackup()">
                                        <i class="fas fa-undo"></i>
                                        恢复备份
                                    </button>
                                </div>
                            </div>

                            <!-- 存储使用情况 -->
                            <div class="data-section">
                                <h3><i class="fas fa-chart-pie"></i> 存储使用情况</h3>
                                <div id="storageUsage" class="storage-usage">
                                    <!-- 存储使用情况将在这里显示 -->
                                </div>
                                <button class="btn btn-secondary" onclick="refreshStorageUsage()">
                                    <i class="fas fa-refresh"></i>
                                    刷新
                                </button>
                            </div>

                            <!-- 数据清理 -->
                            <div class="data-section">
                                <h3><i class="fas fa-trash-alt"></i> 数据清理</h3>
                                <div class="data-actions">
                                    <button class="btn btn-warning" onclick="clearScheduleHistory()">
                                        <i class="fas fa-calendar-times"></i>
                                        清除排班历史
                                    </button>
                                    <button class="btn btn-danger" onclick="clearAllData()">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        清除所有数据
                                    </button>
                                </div>
                                <p class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    注意：清除数据操作不可恢复，请谨慎操作！
                                </p>
                            </div>

                            <!-- 排班历史 -->
                            <div class="data-section">
                                <h3><i class="fas fa-history"></i> 排班历史</h3>
                                <div id="scheduleHistory" class="schedule-history">
                                    <!-- 排班历史将在这里显示 -->
                                </div>
                                <button class="btn btn-secondary" onclick="refreshScheduleHistory()">
                                    <i class="fas fa-refresh"></i>
                                    刷新历史
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 添加员工模态框 -->
    <div id="addEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加员工</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addEmployeeForm">
                    <div class="form-group">
                        <label for="employeeName">员工姓名</label>
                        <input type="text" id="employeeName" name="name" required maxlength="20">
                    </div>
                    <div class="form-group">
                        <label for="employeeSkills">技能标签</label>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-top: 0.5rem;">
                            <label style="display: flex; align-items: center; gap: 0.25rem;">
                                <input type="checkbox" name="skills" value="manager">
                                <span class="skill-tag manager">管理员</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.25rem;">
                                <input type="checkbox" name="skills" value="senior">
                                <span class="skill-tag senior">资深员工</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.25rem;">
                                <input type="checkbox" name="skills" value="trainee">
                                <span class="skill-tag trainee">实习生</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="employeePhone">联系电话</label>
                        <input type="tel" id="employeePhone" name="phone" maxlength="20" placeholder="可选">
                    </div>
                    <div class="form-group">
                        <label for="employeeEmail">邮箱地址</label>
                        <input type="email" id="employeeEmail" name="email" maxlength="50" placeholder="可选">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">确定</button>
                        <button type="button" class="btn btn-secondary modal-close">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑员工模态框 -->
    <div id="editEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑员工</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editEmployeeForm">
                    <input type="hidden" id="editEmployeeId">
                    <div class="form-group">
                        <label for="editEmployeeName">员工姓名</label>
                        <input type="text" id="editEmployeeName" name="name" required maxlength="20">
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeSkills">技能标签</label>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-top: 0.5rem;">
                            <label style="display: flex; align-items: center; gap: 0.25rem;">
                                <input type="checkbox" name="skills" value="manager" id="editSkillManager">
                                <span class="skill-tag manager">管理员</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.25rem;">
                                <input type="checkbox" name="skills" value="senior" id="editSkillSenior">
                                <span class="skill-tag senior">资深员工</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.25rem;">
                                <input type="checkbox" name="skills" value="trainee" id="editSkillTrainee">
                                <span class="skill-tag trainee">实习生</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeePhone">联系电话</label>
                        <input type="tel" id="editEmployeePhone" name="phone" maxlength="20">
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeEmail">邮箱地址</label>
                        <input type="email" id="editEmployeeEmail" name="email" maxlength="50">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">确定</button>
                        <button type="button" class="btn btn-secondary modal-close">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 保存模板模态框 -->
    <div id="saveTemplateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>保存排班模板</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="saveTemplateForm">
                    <div class="form-group">
                        <label for="templateName">模板名称</label>
                        <input type="text" id="templateName" name="name" required maxlength="50" placeholder="例如：标准排班模板">
                    </div>
                    <div class="form-group">
                        <label for="templateDescription">模板描述</label>
                        <textarea id="templateDescription" name="description" rows="3" maxlength="200" placeholder="描述这个模板的特点和适用场景"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">保存</button>
                        <button type="button" class="btn btn-secondary modal-close">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载模板模态框 -->
    <div id="loadTemplateModal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3>加载排班模板</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div id="templateList" class="template-list">
                    <!-- 模板列表将在这里显示 -->
                </div>
                <div class="empty-state" id="noTemplates" style="display: none;">
                    暂无保存的模板
                </div>
            </div>
        </div>
    </div>

    <!-- 请假管理模态框 -->
    <div id="leaveManagementModal" class="modal">
        <div class="modal-content" style="max-width: 700px;">
            <div class="modal-header">
                <h3>请假管理</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 添加请假 -->
                <div class="leave-section">
                    <h4><i class="fas fa-plus"></i> 添加请假</h4>
                    <form id="addLeaveForm">
                        <input type="hidden" id="leaveEmployeeId">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                            <div class="form-group">
                                <label for="leaveStartDate">开始日期</label>
                                <input type="date" id="leaveStartDate" name="startDate" required>
                            </div>
                            <div class="form-group">
                                <label for="leaveEndDate">结束日期</label>
                                <input type="date" id="leaveEndDate" name="endDate" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="leaveReason">请假原因</label>
                            <select id="leaveReason" name="reason" required>
                                <option value="">请选择</option>
                                <option value="sick">病假</option>
                                <option value="personal">事假</option>
                                <option value="annual">年假</option>
                                <option value="maternity">产假</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="leaveNote">备注</label>
                            <textarea id="leaveNote" name="note" rows="2" maxlength="200" placeholder="可选"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">添加请假</button>
                        </div>
                    </form>
                </div>

                <!-- 请假记录 -->
                <div class="leave-section">
                    <h4><i class="fas fa-list"></i> 请假记录</h4>
                    <div id="leaveList" class="leave-list">
                        <!-- 请假记录将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工时统计模态框 -->
    <div id="workHoursModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3>工时统计</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="work-hours-section">
                    <div class="form-group">
                        <label>统计时间范围</label>
                        <div style="display: flex; gap: 1rem; align-items: center;">
                            <input type="date" id="statsStartDate">
                            <span>至</span>
                            <input type="date" id="statsEndDate">
                            <button class="btn btn-primary" onclick="calculateWorkHours()">
                                <i class="fas fa-calculator"></i>
                                计算
                            </button>
                        </div>
                    </div>

                    <div id="workHoursResult" class="work-hours-result">
                        <!-- 工时统计结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message"></div>

    <script>
        // 全局变量
        let employees = [];
        let currentSchedule = null;
        let currentWeek = new Date();
        let isDragMode = false;
        let selectedEmployees = new Set();
        let draggedElement = null;

        // 排班算法类
        class ScheduleAlgorithm {
            constructor() {
                this.SHIFT_TYPES = {
                    'early': '早班(16:30)',
                    'late': '晚班(20:30)',
                    'off': '休息'
                };

                this.WEEKDAYS = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

                // 新增配置项
                this.MAX_CONSECUTIVE_WORK_DAYS = 6; // 最大连续工作天数（周一到周六）
                this.MIN_REST_DAYS_PER_WEEK = 0; // 每周最少休息天数（除周日外，工作日不休息）
                this.PREFERRED_SHIFT_BALANCE_THRESHOLD = 1; // 早晚班平衡阈值

                // 历史记录存储键
                this.HISTORY_STORAGE_KEY = 'paiban_schedule_history';
            }

            generateSchedule(employees, weekStart, existingSchedule = null) {
                // 确保周开始是周一
                const week = new Date(weekStart);
                week.setDate(week.getDate() - week.getDay() + 1);

                // 只过滤活跃的员工，请假处理在具体日期中进行
                const activeEmployees = employees.filter(emp => emp.is_active);

                if (activeEmployees.length === 0) {
                    throw new Error('没有活跃的员工');
                }

                // 获取历史排班记录
                const history = this.getScheduleHistory();
                const lastWeekSchedule = this.getLastWeekSchedule(history, week);

                // 保存当前周开始日期，供分配班次时使用
                this.currentWeekStart = week;

                // 获取今天的日期
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                // 创建7天的排班
                const days = [];

                // 初始化当前正在构建的排班表，供班次分配函数使用
                this.currentScheduleInProgress = {
                    week_start: week.toISOString(),
                    days: [],
                    generated_at: new Date().toISOString()
                };

                for (let i = 0; i < 7; i++) {
                    const date = new Date(week);
                    date.setDate(date.getDate() + i);
                    const weekday = this.WEEKDAYS[date.getDay() === 0 ? 6 : date.getDay() - 1];

                    const assignments = {};

                    // 检查是否是已经过去的日期或今天
                    const dayDate = new Date(date);
                    dayDate.setHours(0, 0, 0, 0);
                    const isPastOrToday = dayDate <= today;

                    // 如果有现有排班且是过去的日期或今天，保持原有安排
                    if (existingSchedule && isPastOrToday && existingSchedule.days[i]) {
                        console.log(`保持 ${date.toDateString()} 的原有排班`);
                        Object.assign(assignments, existingSchedule.days[i].assignments);
                    } else {
                        // 未来的日期重新安排
                        if (weekday === 'sunday') {
                            // 周日所有人休息
                            activeEmployees.forEach(emp => {
                                assignments[emp.id] = 'off';
                            });
                        } else {
                            console.log(`重新安排 ${date.toDateString()} 的排班`);
                            // 工作日智能分配班次
                            this.assignWorkDayShifts(assignments, activeEmployees, i, lastWeekSchedule, history);
                        }
                    }

                    // 重要：无论是保持原有安排还是重新安排，都要检查并更新请假状态
                    // 这确保删除请假记录后能正确更新员工状态
                    this.updateLeaveStatusForDay(assignments, activeEmployees, dayDate);

                    const daySchedule = {
                        date: date.toISOString(),
                        weekday: weekday,
                        assignments: assignments
                    };

                    days.push(daySchedule);
                    // 同时更新正在构建的排班表
                    this.currentScheduleInProgress.days.push(daySchedule);
                }

                const schedule = {
                    week_start: week.toISOString(),
                    days: days,
                    generated_at: new Date().toISOString()
                };

                // 清理临时变量
                this.currentScheduleInProgress = null;

                // 对所有日期进行强化优化，确保初始生成就很均匀
                this.optimizeShiftDistributionForFuture(schedule, activeEmployees, new Date('1900-01-01')); // 对所有日期优化
                this.ensureWorkDayLimitsForFuture(schedule, activeEmployees, new Date('1900-01-01')); // 对所有日期检查
                this.balanceShiftPreferencesForFuture(schedule, activeEmployees, history, new Date('1900-01-01')); // 对所有日期平衡

                // 额外的全局平衡优化
                this.performGlobalBalanceOptimization(schedule, activeEmployees);

                // 只有在实际生成新排班时才保存到历史记录
                // 如果是基于已存在排班的更新，不重复保存
                const isNewSchedule = !existingSchedule || this.hasSignificantChanges(schedule, existingSchedule);
                if (isNewSchedule) {
                    this.saveScheduleToHistory(schedule);
                    console.log('新排班已保存到历史记录');
                } else {
                    console.log('排班基于已存在数据，未重复保存');
                }

                return schedule;
            }

            // 检查员工在指定周是否请假
            getCurrentLeaves(leaves, weekStart) {
                if (!leaves || leaves.length === 0) return [];

                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekEnd.getDate() + 6);

                return leaves.filter(leave => {
                    if (leave.status !== 'approved') return false;

                    const leaveStart = new Date(leave.startDate);
                    const leaveEnd = new Date(leave.endDate);

                    // 检查请假期间是否与排班周重叠
                    return (leaveStart <= weekEnd && leaveEnd >= weekStart);
                });
            }

            // 获取排班历史记录
            getScheduleHistory() {
                try {
                    const history = localStorage.getItem(this.HISTORY_STORAGE_KEY);
                    return history ? JSON.parse(history) : [];
                } catch (e) {
                    console.warn('读取排班历史失败:', e);
                    return [];
                }
            }

            // 检查排班是否有重要变化
            hasSignificantChanges(newSchedule, existingSchedule) {
                if (!existingSchedule) return true;

                // 比较每天的班次分配
                for (let i = 0; i < newSchedule.days.length; i++) {
                    const newDay = newSchedule.days[i];
                    const existingDay = existingSchedule.days[i];

                    if (!existingDay) return true;

                    // 比较班次分配
                    const newAssignments = newDay.assignments || {};
                    const existingAssignments = existingDay.assignments || {};

                    // 检查员工数量是否变化
                    const newEmployeeIds = Object.keys(newAssignments);
                    const existingEmployeeIds = Object.keys(existingAssignments);

                    if (newEmployeeIds.length !== existingEmployeeIds.length) return true;

                    // 检查具体班次分配是否变化
                    for (const empId of newEmployeeIds) {
                        if (newAssignments[empId] !== existingAssignments[empId]) {
                            return true;
                        }
                    }
                }

                return false; // 没有重要变化
            }

            // 保存排班到历史记录
            saveScheduleToHistory(schedule) {
                try {
                    const history = this.getScheduleHistory();

                    // 移除相同周的旧记录
                    const weekStart = new Date(schedule.week_start).toDateString();
                    const filteredHistory = history.filter(h =>
                        new Date(h.week_start).toDateString() !== weekStart
                    );

                    // 添加新记录
                    filteredHistory.push(schedule);

                    // 只保留最近8周的记录
                    const recentHistory = filteredHistory
                        .sort((a, b) => new Date(b.week_start) - new Date(a.week_start))
                        .slice(0, 8);

                    localStorage.setItem(this.HISTORY_STORAGE_KEY, JSON.stringify(recentHistory));
                } catch (e) {
                    console.warn('保存排班历史失败:', e);
                }
            }

            // 获取上周排班记录
            getLastWeekSchedule(history, currentWeek) {
                const lastWeek = new Date(currentWeek);
                lastWeek.setDate(lastWeek.getDate() - 7);
                const lastWeekStr = lastWeek.toDateString();

                return history.find(h =>
                    new Date(h.week_start).toDateString() === lastWeekStr
                );
            }

            // 智能分配工作日班次
            assignWorkDayShifts(assignments, activeEmployees, dayIndex, lastWeekSchedule, history) {
                // 获取当天日期
                const currentWeek = new Date(this.currentWeekStart || new Date());
                const dayDate = new Date(currentWeek);
                dayDate.setDate(dayDate.getDate() + dayIndex);

                // 过滤掉当天请假的员工
                const availableEmployees = activeEmployees.filter(emp => {
                    const leaves = emp.leaves || [];
                    return !this.isEmployeeOnLeaveOnDate(leaves, dayDate);
                });

                if (availableEmployees.length === 0) {
                    // 如果所有人都请假，强制分配给活跃员工
                    console.warn(`${dayDate.toDateString()} 所有员工都请假，强制分配`);
                    availableEmployees.push(...activeEmployees);
                }

                // 计算每个员工的历史班次统计
                const employeeStats = this.calculateEmployeeHistoryStats(availableEmployees, history);

                // 考虑上周同一天的班次，避免连续相同班次
                const lastWeekAssignments = lastWeekSchedule ?
                    lastWeekSchedule.days[dayIndex]?.assignments : {};

                // 获取前一天的班次分配（避免连续相同班次）
                const previousDayShifts = this.getPreviousDayShifts(dayIndex);

                // 按班次不平衡度和连续班次情况排序员工
                const sortedEmployees = [...availableEmployees].sort((a, b) => {
                    const statsA = employeeStats[a.id] || { early: 0, late: 0 };
                    const statsB = employeeStats[b.id] || { early: 0, late: 0 };

                    // 计算班次不平衡度（早班多的排前面分配晚班，晚班多的排前面分配早班）
                    const imbalanceA = statsA.early - statsA.late;
                    const imbalanceB = statsB.early - statsB.late;

                    // 优先考虑班次不平衡度
                    if (imbalanceB !== imbalanceA) {
                        return imbalanceB - imbalanceA;
                    }

                    // 如果不平衡度相同，按员工ID排序确保稳定性
                    return a.id.localeCompare(b.id);
                });

                // 确保每天早晚班人数尽量平均
                const totalEmployees = availableEmployees.length;
                const earlyCount = Math.ceil(totalEmployees / 2);
                const lateCount = totalEmployees - earlyCount;

                let earlyAssigned = 0;
                let lateAssigned = 0;

                // 首先为所有活跃员工设置请假状态
                activeEmployees.forEach(emp => {
                    const leaves = emp.leaves || [];
                    if (this.isEmployeeOnLeaveOnDate(leaves, dayDate)) {
                        assignments[emp.id] = 'leave'; // 请假状态
                    }
                });

                // 然后为可用员工分配具体班次
                sortedEmployees.forEach((emp, index) => {
                    const stats = employeeStats[emp.id] || { early: 0, late: 0 };
                    const previousShift = previousDayShifts[emp.id];
                    const lastWeekShift = lastWeekAssignments[emp.id];
                    let assignedShift;

                    // 根据当前班次分配情况和员工历史记录决定班次
                    if (earlyAssigned < earlyCount && lateAssigned < lateCount) {
                        // 两种班次都还有名额，智能分配
                        assignedShift = this.determineOptimalShift(
                            stats, previousShift, lastWeekShift,
                            earlyAssigned, lateAssigned, earlyCount, lateCount
                        );
                    } else if (earlyAssigned < earlyCount) {
                        assignedShift = 'early'; // 只剩早班名额
                    } else {
                        assignedShift = 'late'; // 只剩晚班名额
                    }

                    // 更新计数
                    if (assignedShift === 'early') {
                        earlyAssigned++;
                    } else {
                        lateAssigned++;
                    }

                    assignments[emp.id] = assignedShift;
                });
            }

            // 获取前一天的班次分配
            getPreviousDayShifts(currentDayIndex) {
                if (currentDayIndex === 0) {
                    // 如果是周一，返回空对象
                    return {};
                }

                // 获取前一天的班次分配
                const previousDayIndex = currentDayIndex - 1;
                const currentSchedule = this.getCurrentScheduleInProgress();

                if (currentSchedule && currentSchedule.days && currentSchedule.days[previousDayIndex]) {
                    return currentSchedule.days[previousDayIndex].assignments || {};
                }

                return {};
            }

            // 获取当前正在生成的排班表
            getCurrentScheduleInProgress() {
                // 这个函数需要访问当前正在构建的排班表
                // 由于JavaScript的作用域限制，我们需要在生成过程中传递这个信息
                return this.currentScheduleInProgress || null;
            }

            // 确定最优班次分配
            determineOptimalShift(stats, previousShift, lastWeekShift, earlyAssigned, lateAssigned, earlyCount, lateCount) {
                // 计算各种因素的权重
                let earlyScore = 0;
                let lateScore = 0;

                // 1. 历史班次平衡（权重最高）
                if (stats.early > stats.late) {
                    lateScore += 10; // 早班多的优先分配晚班
                } else if (stats.late > stats.early) {
                    earlyScore += 10; // 晚班多的优先分配早班
                }

                // 2. 避免连续相同班次（权重较高）
                if (previousShift === 'early') {
                    lateScore += 8; // 前一天早班，优先分配晚班
                } else if (previousShift === 'late') {
                    earlyScore += 8; // 前一天晚班，优先分配早班
                }

                // 3. 避免与上周同一天相同班次（权重中等）
                if (lastWeekShift === 'early') {
                    lateScore += 5; // 上周同一天早班，优先分配晚班
                } else if (lastWeekShift === 'late') {
                    earlyScore += 5; // 上周同一天晚班，优先分配早班
                }

                // 4. 当天班次需求平衡（权重较低）
                const earlyNeed = earlyCount - earlyAssigned;
                const lateNeed = lateCount - lateAssigned;
                if (earlyNeed > lateNeed) {
                    earlyScore += 3;
                } else if (lateNeed > earlyNeed) {
                    lateScore += 3;
                }

                // 5. 确保不会出现无法分配的情况
                if (earlyAssigned >= earlyCount) {
                    return 'late';
                }
                if (lateAssigned >= lateCount) {
                    return 'early';
                }

                // 根据得分决定班次
                return earlyScore >= lateScore ? 'early' : 'late';
            }

            // 全局平衡优化 - 确保整周的早晚班分配尽可能均匀，避免连续相同班次
            performGlobalBalanceOptimization(schedule, employees) {
                const workDays = schedule.days.filter(day => day.weekday !== 'sunday');

                // 统计每个员工的总班次
                const employeeWeeklyStats = {};
                employees.forEach(emp => {
                    employeeWeeklyStats[emp.id] = { early: 0, late: 0, total: 0, consecutive: 0 };
                });

                // 计算当前分配和连续班次
                workDays.forEach(day => {
                    Object.entries(day.assignments).forEach(([empId, shift]) => {
                        if (shift === 'early' || shift === 'late') {
                            employeeWeeklyStats[empId][shift]++;
                            employeeWeeklyStats[empId].total++;
                        }
                    });
                });

                // 计算连续班次数量
                this.calculateConsecutiveShifts(schedule, employees, employeeWeeklyStats);

                // 多轮全局优化
                for (let round = 0; round < 8; round++) {
                    let improved = false;

                    // 第一阶段：优化连续班次问题（前4轮）
                    if (round < 4) {
                        improved = this.optimizeConsecutiveShifts(schedule, employees, employeeWeeklyStats);
                    }

                    // 第二阶段：优化班次平衡问题（后4轮）
                    if (!improved || round >= 4) {
                        improved = this.optimizeShiftBalance(schedule, employees, employeeWeeklyStats) || improved;
                    }

                    if (!improved) break;
                }

                // 输出最终统计
                console.log('全局平衡优化完成，最终统计:');
                employees.forEach(emp => {
                    const stats = employeeWeeklyStats[emp.id];
                    if (stats.total > 0) {
                        console.log(`${emp.name}: 早班${stats.early}次, 晚班${stats.late}次, 差异${Math.abs(stats.early - stats.late)}, 连续班次${stats.consecutive}次`);
                    }
                });
            }

            // 计算连续班次数量
            calculateConsecutiveShifts(schedule, employees, employeeWeeklyStats) {
                const workDays = schedule.days.filter(day => day.weekday !== 'sunday');

                employees.forEach(emp => {
                    let consecutiveCount = 0;
                    let lastShift = null;
                    let currentConsecutive = 0;

                    workDays.forEach(day => {
                        const shift = day.assignments[emp.id];
                        if (shift === 'early' || shift === 'late') {
                            if (shift === lastShift) {
                                currentConsecutive++;
                            } else {
                                if (currentConsecutive > 1) {
                                    consecutiveCount += currentConsecutive - 1; // 连续2天算1次，连续3天算2次
                                }
                                currentConsecutive = 1;
                            }
                            lastShift = shift;
                        } else {
                            if (currentConsecutive > 1) {
                                consecutiveCount += currentConsecutive - 1;
                            }
                            currentConsecutive = 0;
                            lastShift = null;
                        }
                    });

                    // 处理最后的连续班次
                    if (currentConsecutive > 1) {
                        consecutiveCount += currentConsecutive - 1;
                    }

                    employeeWeeklyStats[emp.id].consecutive = consecutiveCount;
                });
            }

            // 优化连续班次问题
            optimizeConsecutiveShifts(schedule, employees, employeeWeeklyStats) {
                const workDays = schedule.days.filter(day => day.weekday !== 'sunday');
                let improved = false;

                // 找出有连续班次问题的员工
                const employeesWithConsecutive = employees.filter(emp =>
                    employeeWeeklyStats[emp.id].consecutive > 0
                );

                if (employeesWithConsecutive.length === 0) return false;

                // 尝试通过交换来减少连续班次
                for (let i = 0; i < workDays.length - 1; i++) {
                    const day1 = workDays[i];
                    const day2 = workDays[i + 1];

                    employeesWithConsecutive.forEach(emp1 => {
                        const shift1Day1 = day1.assignments[emp1.id];
                        const shift1Day2 = day2.assignments[emp1.id];

                        // 如果这个员工连续两天相同班次
                        if (shift1Day1 === shift1Day2 && (shift1Day1 === 'early' || shift1Day1 === 'late')) {
                            // 寻找可以交换的员工
                            employees.forEach(emp2 => {
                                if (emp1.id === emp2.id) return;

                                const shift2Day1 = day1.assignments[emp2.id];
                                const shift2Day2 = day2.assignments[emp2.id];

                                // 检查交换是否能改善连续班次问题
                                if (this.canImproveConsecutiveBySwap(emp1, emp2, i, workDays, employeeWeeklyStats)) {
                                    // 执行交换
                                    day1.assignments[emp1.id] = shift2Day1;
                                    day1.assignments[emp2.id] = shift1Day1;

                                    // 重新计算连续班次
                                    this.calculateConsecutiveShifts(schedule, employees, employeeWeeklyStats);
                                    improved = true;
                                    console.log(`连续班次优化：交换 ${emp1.name} 和 ${emp2.name} 在 ${new Date(day1.date).toDateString()} 的班次`);
                                    return;
                                }
                            });
                        }
                    });

                    if (improved) break;
                }

                return improved;
            }

            // 检查交换是否能改善连续班次问题
            canImproveConsecutiveBySwap(emp1, emp2, dayIndex, workDays, employeeWeeklyStats) {
                // 简化的检查：如果交换不会创造新的连续班次问题，就认为是改善
                const day1 = workDays[dayIndex];
                const day2 = workDays[dayIndex + 1];

                const shift1Day1 = day1.assignments[emp1.id];
                const shift2Day1 = day1.assignments[emp2.id];

                // 检查emp1在交换后是否还有连续班次
                const prevShift1 = dayIndex > 0 ? workDays[dayIndex - 1].assignments[emp1.id] : null;
                const nextShift1 = dayIndex + 2 < workDays.length ? workDays[dayIndex + 2].assignments[emp1.id] : null;

                // 交换后emp1的班次序列
                const newShift1Day1 = shift2Day1;
                const shift1Day2 = day2.assignments[emp1.id];

                // 如果交换后能打破连续班次，就是改善
                const beforeConsecutive = (prevShift1 === shift1Day1) || (shift1Day1 === shift1Day2) || (shift1Day2 === nextShift1);
                const afterConsecutive = (prevShift1 === newShift1Day1) || (newShift1Day1 === shift1Day2) || (shift1Day2 === nextShift1);

                return beforeConsecutive && !afterConsecutive;
            }

            // 优化班次平衡问题
            optimizeShiftBalance(schedule, employees, employeeWeeklyStats) {
                const workDays = schedule.days.filter(day => day.weekday !== 'sunday');
                let improved = false;

                // 找出最不平衡的员工对
                const employeeList = employees.filter(emp => employeeWeeklyStats[emp.id].total > 0);

                for (let i = 0; i < employeeList.length - 1; i++) {
                    for (let j = i + 1; j < employeeList.length; j++) {
                        const emp1 = employeeList[i];
                        const emp2 = employeeList[j];
                        const stats1 = employeeWeeklyStats[emp1.id];
                        const stats2 = employeeWeeklyStats[emp2.id];

                        // 计算不平衡度
                        const imbalance1 = Math.abs(stats1.early - stats1.late);
                        const imbalance2 = Math.abs(stats2.early - stats2.late);
                        const currentTotalImbalance = imbalance1 + imbalance2;

                        // 尝试在某一天交换这两个员工的班次
                        for (const day of workDays) {
                            const shift1 = day.assignments[emp1.id];
                            const shift2 = day.assignments[emp2.id];

                            if (shift1 !== shift2 && shift1 !== 'leave' && shift2 !== 'leave' &&
                                shift1 !== 'off' && shift2 !== 'off') {

                                // 计算交换后的不平衡度
                                const newStats1 = { ...stats1 };
                                const newStats2 = { ...stats2 };

                                newStats1[shift1]--;
                                newStats1[shift2]++;
                                newStats2[shift2]--;
                                newStats2[shift1]++;

                                const newImbalance1 = Math.abs(newStats1.early - newStats1.late);
                                const newImbalance2 = Math.abs(newStats2.early - newStats2.late);
                                const newTotalImbalance = newImbalance1 + newImbalance2;

                                // 如果能改善平衡度，则交换
                                if (newTotalImbalance < currentTotalImbalance) {
                                    day.assignments[emp1.id] = shift2;
                                    day.assignments[emp2.id] = shift1;

                                    // 更新统计
                                    employeeWeeklyStats[emp1.id] = newStats1;
                                    employeeWeeklyStats[emp2.id] = newStats2;

                                    improved = true;
                                    console.log(`班次平衡优化：交换 ${emp1.name} 和 ${emp2.name} 在 ${new Date(day.date).toDateString()} 的班次`);
                                    return true; // 立即返回，进行下一轮优化
                                }
                            }
                        }

                        if (improved) break;
                    }
                    if (improved) break;
                }

                return improved;
            }

            // 更新指定日期的请假状态
            updateLeaveStatusForDay(assignments, activeEmployees, dayDate) {
                const needsReassignment = [];

                // 第一步：更新请假状态，收集需要重新分配的员工
                activeEmployees.forEach(emp => {
                    const isOnLeave = this.isEmployeeOnLeaveOnDate(emp.leaves || [], dayDate);
                    const currentAssignment = assignments[emp.id];

                    if (isOnLeave && currentAssignment !== 'leave') {
                        // 员工请假但当前不是请假状态，设置为请假
                        console.log(`设置 ${emp.name} 在 ${dayDate.toDateString()} 为请假状态`);
                        assignments[emp.id] = 'leave';
                    } else if (!isOnLeave && currentAssignment === 'leave') {
                        // 员工没有请假但当前是请假状态，需要重新分配班次
                        console.log(`${emp.name} 在 ${dayDate.toDateString()} 取消请假，需要重新分配班次`);
                        needsReassignment.push(emp);
                    }
                });

                // 第二步：为需要重新分配的员工分配班次
                if (needsReassignment.length > 0) {
                    // 统计当前的班次分配（排除需要重新分配的员工）
                    const currentAssignments = Object.entries(assignments)
                        .filter(([empId, shift]) =>
                            !needsReassignment.some(emp => emp.id === empId) &&
                            shift !== 'leave' && shift !== 'off'
                        );

                    let earlyCount = currentAssignments.filter(([empId, shift]) => shift === 'early').length;
                    let lateCount = currentAssignments.filter(([empId, shift]) => shift === 'late').length;

                    // 计算理想的班次分配
                    const totalWorkingEmployees = activeEmployees.filter(emp =>
                        !this.isEmployeeOnLeaveOnDate(emp.leaves || [], dayDate)
                    ).length;

                    const idealEarlyCount = Math.ceil(totalWorkingEmployees / 2);
                    const idealLateCount = totalWorkingEmployees - idealEarlyCount;

                    console.log(`当天工作人员${totalWorkingEmployees}人，理想分配：早班${idealEarlyCount}人，晚班${idealLateCount}人`);
                    console.log(`当前分配：早班${earlyCount}人，晚班${lateCount}人，需要重新分配${needsReassignment.length}人`);

                    // 为需要重新分配的员工分配班次
                    needsReassignment.forEach(emp => {
                        let assignedShift;

                        if (earlyCount < idealEarlyCount) {
                            assignedShift = 'early';
                            earlyCount++;
                        } else if (lateCount < idealLateCount) {
                            assignedShift = 'late';
                            lateCount++;
                        } else {
                            // 如果都已达到理想数量，分配到人数较少的班次
                            assignedShift = earlyCount <= lateCount ? 'early' : 'late';
                            if (assignedShift === 'early') earlyCount++;
                            else lateCount++;
                        }

                        assignments[emp.id] = assignedShift;
                        console.log(`重新分配 ${emp.name} 为 ${assignedShift}`);
                    });
                }
            }

            // 只对未来日期优化班次分配
            optimizeShiftDistributionForFuture(schedule, employees, today) {
                const workDays = schedule.days.filter(day => {
                    const dayDate = new Date(day.date);
                    dayDate.setHours(0, 0, 0, 0);
                    return day.weekday !== 'sunday' && dayDate > today;
                });

                if (workDays.length === 0) return; // 没有未来的工作日需要优化

                // 统计每个员工的班次数量（包括过去的日期）
                const employeeShiftCounts = {};
                employees.forEach(emp => {
                    employeeShiftCounts[emp.id] = { early: 0, late: 0 };
                });

                // 计算当前分配（包括所有日期）
                schedule.days.forEach(day => {
                    if (day.weekday !== 'sunday') {
                        Object.entries(day.assignments).forEach(([empId, shift]) => {
                            if (shift === 'early' || shift === 'late') {
                                employeeShiftCounts[empId][shift]++;
                            }
                        });
                    }
                });

                // 只对未来的工作日进行优化
                for (let iteration = 0; iteration < 20; iteration++) {
                    let improved = false;

                    workDays.forEach(day => {
                        const assignments = Object.entries(day.assignments);

                        for (let i = 0; i < assignments.length - 1; i++) {
                            for (let j = i + 1; j < assignments.length; j++) {
                                const [empId1, shift1] = assignments[i];
                                const [empId2, shift2] = assignments[j];

                                if (shift1 !== 'off' && shift1 !== 'leave' &&
                                    shift2 !== 'off' && shift2 !== 'leave' &&
                                    shift1 !== shift2) {

                                    const counts1 = employeeShiftCounts[empId1];
                                    const counts2 = employeeShiftCounts[empId2];

                                    // 计算当前不平衡度
                                    const currentImbalance = Math.abs(counts1.early - counts1.late) +
                                                           Math.abs(counts2.early - counts2.late);

                                    // 计算交换后的不平衡度
                                    const newCounts1 = { ...counts1 };
                                    const newCounts2 = { ...counts2 };

                                    newCounts1[shift1]--;
                                    newCounts1[shift2]++;
                                    newCounts2[shift2]--;
                                    newCounts2[shift1]++;

                                    const newImbalance = Math.abs(newCounts1.early - newCounts1.late) +
                                                       Math.abs(newCounts2.early - newCounts2.late);

                                    // 如果交换能改善平衡度，则交换
                                    if (newImbalance < currentImbalance) {
                                        day.assignments[empId1] = shift2;
                                        day.assignments[empId2] = shift1;
                                        employeeShiftCounts[empId1] = newCounts1;
                                        employeeShiftCounts[empId2] = newCounts2;
                                        improved = true;
                                    }
                                }
                            }
                        }
                    });

                    if (!improved) break;
                }
            }

            // 只对未来日期确保工作日限制
            ensureWorkDayLimitsForFuture(schedule, employees, today) {
                const workDays = schedule.days.filter(day => {
                    const dayDate = new Date(day.date);
                    dayDate.setHours(0, 0, 0, 0);
                    return day.weekday !== 'sunday' && dayDate > today;
                });

                workDays.forEach(day => {
                    const assignments = Object.values(day.assignments);
                    const earlyCount = assignments.filter(s => s === 'early').length;
                    const lateCount = assignments.filter(s => s === 'late').length;
                    const totalEmployees = employees.length;

                    // 检查每天的班次分配是否平衡
                    const targetEarlyCount = Math.ceil(totalEmployees / 2);
                    const targetLateCount = totalEmployees - targetEarlyCount;

                    if (earlyCount > targetEarlyCount) {
                        // 早班人数过多，将一些人调到晚班
                        const excess = earlyCount - targetEarlyCount;
                        let adjusted = 0;

                        Object.entries(day.assignments).forEach(([empId, shift]) => {
                            if (shift === 'early' && adjusted < excess) {
                                day.assignments[empId] = 'late';
                                adjusted++;
                            }
                        });
                    } else if (lateCount > targetLateCount) {
                        // 晚班人数过多，将一些人调到早班
                        const excess = lateCount - targetLateCount;
                        let adjusted = 0;

                        Object.entries(day.assignments).forEach(([empId, shift]) => {
                            if (shift === 'late' && adjusted < excess) {
                                day.assignments[empId] = 'early';
                                adjusted++;
                            }
                        });
                    }
                });
            }

            // 只对未来日期平衡班次偏好
            balanceShiftPreferencesForFuture(schedule, employees, history, today) {
                const workDays = schedule.days.filter(day => {
                    const dayDate = new Date(day.date);
                    dayDate.setHours(0, 0, 0, 0);
                    return day.weekday !== 'sunday' && dayDate > today;
                });

                if (workDays.length === 0) return;

                const historyStats = this.calculateEmployeeHistoryStats(employees, history);

                // 多轮微调
                for (let round = 0; round < 3; round++) {
                    let improved = false;

                    workDays.forEach(day => {
                        const assignments = Object.entries(day.assignments);

                        // 寻找可以改善平衡的交换
                        for (let i = 0; i < assignments.length - 1; i++) {
                            for (let j = i + 1; j < assignments.length; j++) {
                                const [empId1, shift1] = assignments[i];
                                const [empId2, shift2] = assignments[j];

                                if (shift1 !== 'off' && shift1 !== 'leave' &&
                                    shift2 !== 'off' && shift2 !== 'leave' &&
                                    shift1 !== shift2) {

                                    const stats1 = historyStats[empId1] || { early: 0, late: 0 };
                                    const stats2 = historyStats[empId2] || { early: 0, late: 0 };

                                    // 计算交换前后的历史平衡度
                                    const beforeBalance = Math.abs(stats1.early - stats1.late) +
                                                        Math.abs(stats2.early - stats2.late);

                                    const afterBalance = Math.abs((stats1.early + (shift2 === 'early' ? 1 : 0) - (shift1 === 'early' ? 1 : 0)) -
                                                                (stats1.late + (shift2 === 'late' ? 1 : 0) - (shift1 === 'late' ? 1 : 0))) +
                                                       Math.abs((stats2.early + (shift1 === 'early' ? 1 : 0) - (shift2 === 'early' ? 1 : 0)) -
                                                                (stats2.late + (shift1 === 'late' ? 1 : 0) - (shift2 === 'late' ? 1 : 0)));

                                    // 如果能改善历史平衡度，则交换
                                    if (afterBalance < beforeBalance) {
                                        day.assignments[empId1] = shift2;
                                        day.assignments[empId2] = shift1;

                                        // 更新历史统计
                                        if (shift1 === 'early') stats1.early--; else stats1.late--;
                                        if (shift2 === 'early') stats1.early++; else stats1.late++;
                                        if (shift2 === 'early') stats2.early--; else stats2.late--;
                                        if (shift1 === 'early') stats2.early++; else stats2.late++;

                                        improved = true;
                                    }
                                }
                            }
                        }
                    });

                    if (!improved) break;
                }
            }

            // 检查员工在指定日期是否请假
            isEmployeeOnLeaveOnDate(leaves, date) {
                if (!leaves || leaves.length === 0) return false;

                const checkDate = new Date(date);
                checkDate.setHours(0, 0, 0, 0);

                return leaves.some(leave => {
                    if (leave.status !== 'approved') return false;

                    const startDate = new Date(leave.startDate);
                    const endDate = new Date(leave.endDate);
                    startDate.setHours(0, 0, 0, 0);
                    endDate.setHours(23, 59, 59, 999);

                    return checkDate >= startDate && checkDate <= endDate;
                });
            }

            // 计算员工历史班次统计
            calculateEmployeeHistoryStats(employees, history) {
                const stats = {};

                employees.forEach(emp => {
                    stats[emp.id] = { early: 0, late: 0, off: 0 };
                });

                // 统计最近4周的班次分配
                const recentHistory = history
                    .sort((a, b) => new Date(b.week_start) - new Date(a.week_start))
                    .slice(0, 4);

                recentHistory.forEach(schedule => {
                    schedule.days.forEach(day => {
                        if (day.weekday !== 'sunday') {
                            Object.entries(day.assignments).forEach(([empId, shift]) => {
                                if (stats[empId] && shift in stats[empId]) {
                                    stats[empId][shift]++;
                                }
                            });
                        }
                    });
                });

                return stats;
            }

            optimizeShiftDistribution(schedule, employees) {
                const workDays = schedule.days.filter(day => day.weekday !== 'sunday');

                // 统计每个员工的班次数量
                const employeeShiftCounts = {};
                employees.forEach(emp => {
                    employeeShiftCounts[emp.id] = { early: 0, late: 0 };
                });

                // 计算当前分配
                workDays.forEach(day => {
                    Object.entries(day.assignments).forEach(([empId, shift]) => {
                        if (shift === 'early' || shift === 'late') {
                            employeeShiftCounts[empId][shift]++;
                        }
                    });
                });

                // 多轮优化，确保早晚班平衡
                for (let iteration = 0; iteration < 20; iteration++) {
                    let improved = false;

                    // 找出班次最不平衡的员工
                    const employeeImbalances = employees.map(emp => {
                        const counts = employeeShiftCounts[emp.id];
                        return {
                            id: emp.id,
                            imbalance: Math.abs(counts.early - counts.late),
                            earlyCount: counts.early,
                            lateCount: counts.late,
                            needsMore: counts.early > counts.late ? 'late' : 'early'
                        };
                    }).sort((a, b) => b.imbalance - a.imbalance);

                    // 尝试为最不平衡的员工找到交换机会
                    for (let i = 0; i < Math.min(employeeImbalances.length, 3); i++) {
                        const emp1 = employeeImbalances[i];
                        if (emp1.imbalance <= 1) break; // 如果差异已经很小，停止优化

                        // 寻找可以交换的员工
                        for (let j = i + 1; j < employeeImbalances.length; j++) {
                            const emp2 = employeeImbalances[j];

                            // 如果两个员工需要的班次类型相反，尝试交换
                            if (emp1.needsMore !== emp2.needsMore) {
                                const swapped = this.trySwapShifts(workDays, emp1.id, emp2.id, emp1.needsMore, employeeShiftCounts);
                                if (swapped) {
                                    improved = true;
                                    break;
                                }
                            }
                        }

                        if (improved) break;
                    }

                    if (!improved) break;
                }
            }

            // 尝试交换两个员工的班次
            trySwapShifts(workDays, empId1, empId2, targetShift, employeeShiftCounts) {
                const sourceShift = targetShift === 'early' ? 'late' : 'early';

                for (const day of workDays) {
                    const shift1 = day.assignments[empId1];
                    const shift2 = day.assignments[empId2];

                    // 找到可以交换的情况：emp1有sourceShift，emp2有targetShift
                    if (shift1 === sourceShift && shift2 === targetShift) {
                        // 执行交换
                        day.assignments[empId1] = targetShift;
                        day.assignments[empId2] = sourceShift;

                        // 更新统计
                        employeeShiftCounts[empId1][sourceShift]--;
                        employeeShiftCounts[empId1][targetShift]++;
                        employeeShiftCounts[empId2][targetShift]--;
                        employeeShiftCounts[empId2][sourceShift]++;

                        return true;
                    }
                }

                return false;
            }

            // 确保工作日限制（修改为周一到周六都上班）
            ensureWorkDayLimits(schedule, employees) {
                const workDays = schedule.days.filter(day => day.weekday !== 'sunday');

                employees.forEach(emp => {
                    // 确保周一到周六都有班次分配（不允许休息）
                    workDays.forEach(day => {
                        const shift = day.assignments[emp.id];
                        if (!shift || shift === 'off') {
                            // 如果没有分配班次或分配了休息，重新分配
                            // 根据当天的班次分布决定分配早班还是晚班
                            const dayAssignments = Object.values(day.assignments);
                            const earlyCount = dayAssignments.filter(s => s === 'early').length;
                            const lateCount = dayAssignments.filter(s => s === 'late').length;

                            // 分配到人数较少的班次
                            day.assignments[emp.id] = earlyCount <= lateCount ? 'early' : 'late';
                        }
                    });
                });

                // 检查每天的班次分配是否平衡
                workDays.forEach(day => {
                    const assignments = Object.values(day.assignments);
                    const earlyCount = assignments.filter(s => s === 'early').length;
                    const lateCount = assignments.filter(s => s === 'late').length;
                    const totalEmployees = employees.length;

                    // 如果班次分配不平衡，进行调整
                    const targetEarlyCount = Math.ceil(totalEmployees / 2);
                    const targetLateCount = totalEmployees - targetEarlyCount;

                    if (earlyCount > targetEarlyCount) {
                        // 早班人数过多，将一些人调到晚班
                        const excess = earlyCount - targetEarlyCount;
                        let adjusted = 0;

                        Object.entries(day.assignments).forEach(([empId, shift]) => {
                            if (shift === 'early' && adjusted < excess) {
                                day.assignments[empId] = 'late';
                                adjusted++;
                            }
                        });
                    } else if (lateCount > targetLateCount) {
                        // 晚班人数过多，将一些人调到早班
                        const excess = lateCount - targetLateCount;
                        let adjusted = 0;

                        Object.entries(day.assignments).forEach(([empId, shift]) => {
                            if (shift === 'late' && adjusted < excess) {
                                day.assignments[empId] = 'early';
                                adjusted++;
                            }
                        });
                    }
                });
            }

            // 平衡班次偏好
            balanceShiftPreferences(schedule, employees, history) {
                const workDays = schedule.days.filter(day => day.weekday !== 'sunday');
                const historyStats = this.calculateEmployeeHistoryStats(employees, history);

                // 多轮微调
                for (let round = 0; round < 3; round++) {
                    let improved = false;

                    workDays.forEach(day => {
                        const assignments = Object.entries(day.assignments);

                        // 寻找可以改善平衡的交换
                        for (let i = 0; i < assignments.length - 1; i++) {
                            for (let j = i + 1; j < assignments.length; j++) {
                                const [empId1, shift1] = assignments[i];
                                const [empId2, shift2] = assignments[j];

                                if (shift1 !== 'off' && shift2 !== 'off' && shift1 !== shift2) {
                                    const stats1 = historyStats[empId1] || { early: 0, late: 0 };
                                    const stats2 = historyStats[empId2] || { early: 0, late: 0 };

                                    // 计算交换前后的历史平衡度
                                    const beforeBalance = Math.abs(stats1.early - stats1.late) +
                                                        Math.abs(stats2.early - stats2.late);

                                    const afterBalance = Math.abs((stats1.early + (shift2 === 'early' ? 1 : 0) - (shift1 === 'early' ? 1 : 0)) -
                                                                (stats1.late + (shift2 === 'late' ? 1 : 0) - (shift1 === 'late' ? 1 : 0))) +
                                                       Math.abs((stats2.early + (shift1 === 'early' ? 1 : 0) - (shift2 === 'early' ? 1 : 0)) -
                                                                (stats2.late + (shift1 === 'late' ? 1 : 0) - (shift2 === 'late' ? 1 : 0)));

                                    // 如果能改善历史平衡度，则交换
                                    if (afterBalance < beforeBalance) {
                                        day.assignments[empId1] = shift2;
                                        day.assignments[empId2] = shift1;

                                        // 更新历史统计
                                        if (shift1 === 'early') stats1.early--; else stats1.late--;
                                        if (shift2 === 'early') stats1.early++; else stats1.late++;
                                        if (shift2 === 'early') stats2.early--; else stats2.late--;
                                        if (shift1 === 'early') stats2.early++; else stats2.late++;

                                        improved = true;
                                    }
                                }
                            }
                        }
                    });

                    if (!improved) break;
                }
            }

            validateSchedule(schedule, employees) {
                const errors = [];
                const warnings = [];

                const stats = this.calculateStats(schedule, employees);
                const workDays = schedule.days.filter(day => day.weekday !== 'sunday');

                // 检查周日是否都休息
                stats.forEach(stat => {
                    if (!stat.sunday_off) {
                        errors.push(`${stat.employee_name} 周日没有休息`);
                    }
                });

                // 检查周一到周六是否都有人上班（排除请假）
                employees.filter(emp => emp.is_active).forEach(emp => {
                    let workDayOffCount = 0;

                    workDays.forEach(day => {
                        const shift = day.assignments[emp.id];
                        if (!shift || (shift === 'off' && shift !== 'leave')) {
                            workDayOffCount++;
                        }
                    });

                    if (workDayOffCount > 0) {
                        errors.push(`${emp.name} 在工作日(周一到周六)有${workDayOffCount}天休息，应该每天都上班（请假除外）`);
                    }
                });

                // 检查早晚班分配是否平衡（这是核心要求）
                stats.forEach(stat => {
                    const shiftDifference = Math.abs(stat.early_shifts - stat.late_shifts);
                    if (shiftDifference > 1) {
                        warnings.push(`${stat.employee_name} 早晚班分配不平衡 (早班${stat.early_shifts}次，晚班${stat.late_shifts}次，差异${shiftDifference}次)`);
                    }
                });

                // 检查早晚班分配是否平均
                if (stats.length > 0) {
                    const avgEarly = stats.reduce((sum, stat) => sum + stat.early_shifts, 0) / stats.length;
                    const avgLate = stats.reduce((sum, stat) => sum + stat.late_shifts, 0) / stats.length;

                    stats.forEach(stat => {
                        const earlyDiff = Math.abs(stat.early_shifts - avgEarly);
                        const lateDiff = Math.abs(stat.late_shifts - avgLate);

                        if (earlyDiff > this.PREFERRED_SHIFT_BALANCE_THRESHOLD) {
                            warnings.push(`${stat.employee_name} 早班分配不均衡 (${stat.early_shifts}班，平均${avgEarly.toFixed(1)}班)`);
                        }

                        if (lateDiff > this.PREFERRED_SHIFT_BALANCE_THRESHOLD) {
                            warnings.push(`${stat.employee_name} 晚班分配不均衡 (${stat.late_shifts}班，平均${avgLate.toFixed(1)}班)`);
                        }
                    });
                }

                // 检查每日人员配置（周一到周六）
                workDays.forEach((day, index) => {
                    const dayName = ['周一', '周二', '周三', '周四', '周五', '周六'][index];
                    const earlyCount = Object.values(day.assignments).filter(shift => shift === 'early').length;
                    const lateCount = Object.values(day.assignments).filter(shift => shift === 'late').length;
                    const offCount = Object.values(day.assignments).filter(shift => shift === 'off').length;

                    if (earlyCount === 0) {
                        errors.push(`${dayName} 没有早班人员`);
                    }
                    if (lateCount === 0) {
                        errors.push(`${dayName} 没有晚班人员`);
                    }
                    if (offCount > 0) {
                        errors.push(`${dayName} 有${offCount}人休息，工作日不应该有人休息`);
                    }

                    const totalWorking = earlyCount + lateCount;
                    const activeEmployeeCount = employees.filter(emp => emp.is_active).length;

                    // 检查是否所有活跃员工都在工作
                    if (totalWorking !== activeEmployeeCount) {
                        errors.push(`${dayName} 工作人员数量不正确 (${totalWorking}人工作，应该是${activeEmployeeCount}人)`);
                    }

                    // 检查早晚班分配是否相对平衡
                    const idealEarlyCount = Math.ceil(activeEmployeeCount / 2);
                    const idealLateCount = activeEmployeeCount - idealEarlyCount;

                    if (Math.abs(earlyCount - idealEarlyCount) > 1) {
                        warnings.push(`${dayName} 早班人数不够平衡 (${earlyCount}人，理想${idealEarlyCount}人)`);
                    }
                    if (Math.abs(lateCount - idealLateCount) > 1) {
                        warnings.push(`${dayName} 晚班人数不够平衡 (${lateCount}人，理想${idealLateCount}人)`);
                    }
                });

                return {
                    is_valid: errors.length === 0,
                    errors: errors,
                    warnings: warnings,
                    summary: {
                        total_errors: errors.length,
                        total_warnings: warnings.length,
                        validation_score: Math.max(0, 100 - errors.length * 20 - warnings.length * 5)
                    }
                };
            }

            calculateStats(schedule, employees) {
                const employeeDict = {};
                employees.forEach(emp => {
                    employeeDict[emp.id] = emp;
                });

                const stats = [];

                employees.forEach(emp => {
                    let earlyShifts = 0;
                    let lateShifts = 0;
                    let offDays = 0;
                    let sundayOff = false;

                    schedule.days.forEach(day => {
                        const shift = day.assignments[emp.id] || 'off';

                        if (shift === 'early') {
                            earlyShifts++;
                        } else if (shift === 'late') {
                            lateShifts++;
                        } else if (shift === 'off') {
                            offDays++;
                            if (day.weekday === 'sunday') {
                                sundayOff = true;
                            }
                        }
                    });

                    stats.push({
                        employee_id: emp.id,
                        employee_name: emp.name,
                        early_shifts: earlyShifts,
                        late_shifts: lateShifts,
                        off_days: offDays,
                        sunday_off: sundayOff
                    });
                });

                return stats;
            }
        }

        // 数据管理类
        class DataManager {
            constructor() {
                this.STORAGE_KEYS = {
                    employees: 'paiban_employees',
                    schedules: 'paiban_schedule_history',
                    settings: 'paiban_settings',
                    backup: 'paiban_backup'
                };
            }

            // 导出所有数据
            exportAllData() {
                const data = {
                    employees: this.getStorageData(this.STORAGE_KEYS.employees),
                    schedules: this.getStorageData(this.STORAGE_KEYS.schedules),
                    settings: this.getStorageData(this.STORAGE_KEYS.settings),
                    exportDate: new Date().toISOString(),
                    version: '1.0'
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `paiban_backup_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                return data;
            }

            // 导入数据
            importData(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = JSON.parse(e.target.result);

                            // 验证数据格式
                            if (!this.validateImportData(data)) {
                                reject(new Error('数据格式不正确'));
                                return;
                            }

                            // 备份当前数据
                            this.createBackup();

                            // 导入数据
                            if (data.employees) {
                                localStorage.setItem(this.STORAGE_KEYS.employees, JSON.stringify(data.employees));
                            }
                            if (data.schedules) {
                                localStorage.setItem(this.STORAGE_KEYS.schedules, JSON.stringify(data.schedules));
                            }
                            if (data.settings) {
                                localStorage.setItem(this.STORAGE_KEYS.settings, JSON.stringify(data.settings));
                            }

                            resolve(data);
                        } catch (error) {
                            reject(new Error('文件解析失败: ' + error.message));
                        }
                    };
                    reader.readAsText(file);
                });
            }

            // 创建备份
            createBackup() {
                const backup = {
                    employees: this.getStorageData(this.STORAGE_KEYS.employees),
                    schedules: this.getStorageData(this.STORAGE_KEYS.schedules),
                    settings: this.getStorageData(this.STORAGE_KEYS.settings),
                    backupDate: new Date().toISOString()
                };

                localStorage.setItem(this.STORAGE_KEYS.backup, JSON.stringify(backup));
                return backup;
            }

            // 恢复备份
            restoreBackup() {
                try {
                    const backup = this.getStorageData(this.STORAGE_KEYS.backup);
                    if (!backup) {
                        throw new Error('没有找到备份数据');
                    }

                    if (backup.employees) {
                        localStorage.setItem(this.STORAGE_KEYS.employees, JSON.stringify(backup.employees));
                    }
                    if (backup.schedules) {
                        localStorage.setItem(this.STORAGE_KEYS.schedules, JSON.stringify(backup.schedules));
                    }
                    if (backup.settings) {
                        localStorage.setItem(this.STORAGE_KEYS.settings, JSON.stringify(backup.settings));
                    }

                    return backup;
                } catch (error) {
                    throw new Error('恢复备份失败: ' + error.message);
                }
            }

            // 清除所有数据
            clearAllData() {
                Object.values(this.STORAGE_KEYS).forEach(key => {
                    localStorage.removeItem(key);
                });
            }

            // 获取存储数据
            getStorageData(key) {
                try {
                    const data = localStorage.getItem(key);
                    return data ? JSON.parse(data) : null;
                } catch (error) {
                    console.error(`读取${key}数据失败:`, error);
                    return null;
                }
            }

            // 验证导入数据格式
            validateImportData(data) {
                if (!data || typeof data !== 'object') return false;

                // 验证员工数据
                if (data.employees && !Array.isArray(data.employees)) return false;
                if (data.employees) {
                    for (const emp of data.employees) {
                        if (!emp.id || !emp.name || typeof emp.is_active !== 'boolean') {
                            return false;
                        }
                    }
                }

                // 验证排班数据
                if (data.schedules && !Array.isArray(data.schedules)) return false;

                return true;
            }

            // 获取存储使用情况
            getStorageUsage() {
                let totalSize = 0;
                const usage = {};

                Object.entries(this.STORAGE_KEYS).forEach(([name, key]) => {
                    const data = localStorage.getItem(key);
                    const size = data ? new Blob([data]).size : 0;
                    usage[name] = {
                        size: size,
                        sizeFormatted: this.formatBytes(size)
                    };
                    totalSize += size;
                });

                return {
                    ...usage,
                    total: {
                        size: totalSize,
                        sizeFormatted: this.formatBytes(totalSize)
                    }
                };
            }

            // 格式化字节大小
            formatBytes(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        }

        // 员工管理类
        class EmployeeManager {
            constructor() {
                this.storageKey = 'paiban_employees';
            }

            generateId() {
                return Date.now().toString(36) + Math.random().toString(36).substr(2);
            }

            getDefaultEmployees() {
                return [
                    {
                        id: 'emp1',
                        name: '张三',
                        is_active: true,
                        skills: ['manager'],
                        phone: '',
                        email: '',
                        leaves: []
                    },
                    {
                        id: 'emp2',
                        name: '李四',
                        is_active: true,
                        skills: ['senior'],
                        phone: '',
                        email: '',
                        leaves: []
                    },
                    {
                        id: 'emp3',
                        name: '王五',
                        is_active: true,
                        skills: [],
                        phone: '',
                        email: '',
                        leaves: []
                    },
                    {
                        id: 'emp4',
                        name: '赵六',
                        is_active: true,
                        skills: [],
                        phone: '',
                        email: '',
                        leaves: []
                    },
                    {
                        id: 'emp5',
                        name: '钱七',
                        is_active: true,
                        skills: ['trainee'],
                        phone: '',
                        email: '',
                        leaves: []
                    },
                    {
                        id: 'emp6',
                        name: '孙八',
                        is_active: true,
                        skills: [],
                        phone: '',
                        email: '',
                        leaves: []
                    }
                ];
            }

            loadEmployees() {
                try {
                    const stored = localStorage.getItem(this.storageKey);
                    if (stored) {
                        const employees = JSON.parse(stored);
                        if (Array.isArray(employees) && employees.every(emp =>
                            emp.id && emp.name && typeof emp.is_active === 'boolean'
                        )) {
                            // 确保员工数据包含新字段
                            return employees.map(emp => ({
                                ...emp,
                                skills: emp.skills || [],
                                phone: emp.phone || '',
                                email: emp.email || '',
                                leaves: emp.leaves || []
                            }));
                        }
                    }
                } catch (error) {
                    console.error('加载员工数据失败:', error);
                }

                return this.getDefaultEmployees();
            }

            saveEmployees(employees) {
                try {
                    localStorage.setItem(this.storageKey, JSON.stringify(employees));
                } catch (error) {
                    console.error('保存员工数据失败:', error);
                }
            }

            validateName(name, employees, excludeId = null) {
                const errors = [];

                if (!name || !name.trim()) {
                    errors.push('员工姓名不能为空');
                }

                if (name.trim().length > 20) {
                    errors.push('员工姓名不能超过20个字符');
                }

                const trimmedName = name.trim();
                const isDuplicate = employees.some(emp =>
                    emp.name === trimmedName && emp.id !== excludeId
                );

                if (isDuplicate) {
                    errors.push('员工姓名已存在');
                }

                return errors;
            }
        }

        // 初始化管理器
        const scheduleAlgorithm = new ScheduleAlgorithm();
        const employeeManager = new EmployeeManager();
        const dataManager = new DataManager();

        // DOM元素
        const elements = {
            navTabs: document.querySelectorAll('.nav-tab'),
            tabContents: document.querySelectorAll('.tab-content'),
            employeesList: document.getElementById('employeesList'),
            addEmployeeBtn: document.getElementById('addEmployeeBtn'),
            addEmployeeModal: document.getElementById('addEmployeeModal'),
            editEmployeeModal: document.getElementById('editEmployeeModal'),
            addEmployeeForm: document.getElementById('addEmployeeForm'),
            editEmployeeForm: document.getElementById('editEmployeeForm'),
            generateBtn: document.getElementById('generateBtn'),
            scheduleTable: document.getElementById('scheduleTable'),
            statsTable: document.getElementById('statsTable'),
            validationResult: document.getElementById('validationResult'),
            currentWeekSpan: document.getElementById('currentWeek'),
            prevWeekBtn: document.getElementById('prevWeekBtn'),
            nextWeekBtn: document.getElementById('nextWeekBtn'),
            message: document.getElementById('message'),
            storageUsage: document.getElementById('storageUsage'),
            scheduleHistory: document.getElementById('scheduleHistory'),
            importFile: document.getElementById('importFile'),
            saveTemplateModal: document.getElementById('saveTemplateModal'),
            loadTemplateModal: document.getElementById('loadTemplateModal'),
            saveTemplateForm: document.getElementById('saveTemplateForm'),
            templateList: document.getElementById('templateList'),
            batchOperations: document.getElementById('batchOperations'),
            conflictAlerts: document.getElementById('conflictAlerts'),
            dragModeText: document.getElementById('dragModeText'),
            leaveManagementModal: document.getElementById('leaveManagementModal'),
            workHoursModal: document.getElementById('workHoursModal'),
            addLeaveForm: document.getElementById('addLeaveForm'),
            leaveList: document.getElementById('leaveList')
        };

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            initEventListeners();
            loadEmployees();
            updateCurrentWeekDisplay();
            refreshStorageUsage();
            refreshScheduleHistory();
        });

        // 初始化事件监听器
        function initEventListeners() {
            // 导航标签切换
            elements.navTabs.forEach(tab => {
                tab.addEventListener('click', () => switchTab(tab.dataset.tab));
            });

            // 添加员工
            elements.addEmployeeBtn.addEventListener('click', () => showModal('addEmployeeModal'));
            elements.addEmployeeForm.addEventListener('submit', handleAddEmployee);

            // 编辑员工
            elements.editEmployeeForm.addEventListener('submit', handleEditEmployee);

            // 生成排班
            elements.generateBtn.addEventListener('click', generateSchedule);

            // 周切换
            elements.prevWeekBtn.addEventListener('click', () => changeWeek(-1));
            elements.nextWeekBtn.addEventListener('click', () => changeWeek(1));

            // 模态框关闭
            document.querySelectorAll('.modal-close').forEach(btn => {
                btn.addEventListener('click', closeModals);
            });

            // 点击模态框外部关闭
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) closeModals();
                });
            });

            // 保存模板表单
            elements.saveTemplateForm.addEventListener('submit', handleSaveTemplate);

            // 请假表单
            elements.addLeaveForm.addEventListener('submit', handleAddLeave);
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新导航标签状态
            elements.navTabs.forEach(tab => {
                tab.classList.toggle('active', tab.dataset.tab === tabName);
            });

            // 更新内容区域
            elements.tabContents.forEach(content => {
                content.classList.toggle('active', content.id === `${tabName}-tab`);
            });

            // 根据标签页加载相应数据
            if (tabName === 'stats' && currentSchedule) {
                loadStats();
            } else if (tabName === 'data') {
                refreshStorageUsage();
                refreshScheduleHistory();
            }
        }

        // 显示模态框
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('show');
        }

        // 关闭所有模态框
        function closeModals() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('show');
            });
            // 清空表单
            document.querySelectorAll('form').forEach(form => form.reset());
        }

        // 显示消息
        function showMessage(text, type = 'success') {
            elements.message.textContent = text;
            elements.message.className = `message ${type} show`;

            setTimeout(() => {
                elements.message.classList.remove('show');
            }, 3000);
        }

        // 加载员工列表
        function loadEmployees() {
            employees = employeeManager.loadEmployees();
            renderEmployeesList();
        }

        // 保存员工列表
        function saveEmployees() {
            employeeManager.saveEmployees(employees);
        }

        // 渲染员工列表
        function renderEmployeesList() {
            if (employees.length === 0) {
                elements.employeesList.innerHTML = '<div class="empty-state">暂无员工，请添加员工后开始排班</div>';
                return;
            }

            elements.employeesList.innerHTML = employees.map(emp => {
                const skills = emp.skills || [];
                const currentLeaves = getCurrentLeaves(emp.leaves || []);
                const isOnLeave = currentLeaves.length > 0;

                // 调试信息
                console.log(`员工 ${emp.name}:`, {
                    leaves: emp.leaves || [],
                    currentLeaves: currentLeaves,
                    isOnLeave: isOnLeave
                });

                return `
                <div class="employee-item">
                    <div class="employee-info">
                        <div>
                            <span class="employee-name">${emp.name}</span>
                            <span class="employee-status ${emp.is_active ? 'active' : 'inactive'}">
                                ${emp.is_active ? '已启用' : '已停用'}
                            </span>
                            ${isOnLeave ? '<span class="employee-status" style="background-color: #fff3cd; color: #856404;">请假中</span>' : ''}
                        </div>
                        ${skills.length > 0 ? `
                            <div class="skill-tags">
                                ${skills.map(skill => `<span class="skill-tag ${skill}">${getSkillDisplayName(skill)}</span>`).join('')}
                            </div>
                        ` : ''}
                        ${emp.phone || emp.email ? `
                            <div style="font-size: 0.85rem; color: #666; margin-top: 0.25rem;">
                                ${emp.phone ? `<i class="fas fa-phone"></i> ${emp.phone}` : ''}
                                ${emp.phone && emp.email ? ' | ' : ''}
                                ${emp.email ? `<i class="fas fa-envelope"></i> ${emp.email}` : ''}
                            </div>
                        ` : ''}
                    </div>
                    <div class="employee-actions">
                        <button class="btn btn-sm btn-secondary" onclick="showLeaveManagement('${emp.id}')">
                            <i class="fas fa-calendar-times"></i>
                            请假
                        </button>
                        <button class="btn btn-sm btn-info" onclick="showWorkHours('${emp.id}')">
                            <i class="fas fa-clock"></i>
                            工时
                        </button>
                        <button class="btn btn-sm ${emp.is_active ? 'btn-warning' : 'btn-success'}"
                                onclick="toggleEmployeeStatus('${emp.id}')">
                            <i class="fas ${emp.is_active ? 'fa-pause' : 'fa-play'}"></i>
                            ${emp.is_active ? '停用' : '启用'}
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editEmployee('${emp.id}')">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteEmployee('${emp.id}')">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
                `;
            }).join('');

            // 更新统计信息
            updateEmployeeStats();
        }

        // 添加员工
        function handleAddEmployee(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const name = formData.get('name').trim();
            const phone = formData.get('phone').trim();
            const email = formData.get('email').trim();
            const skills = Array.from(formData.getAll('skills'));

            const errors = employeeManager.validateName(name, employees);
            if (errors.length > 0) {
                showMessage(errors[0], 'error');
                return;
            }

            const newEmployee = {
                id: employeeManager.generateId(),
                name: name,
                is_active: true,
                skills: skills,
                phone: phone,
                email: email,
                leaves: []
            };

            employees.push(newEmployee);
            saveEmployees();
            renderEmployeesList();
            closeModals();
            showMessage('员工添加成功');
        }

        // 编辑员工
        function editEmployee(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) return;

            document.getElementById('editEmployeeId').value = employee.id;
            document.getElementById('editEmployeeName').value = employee.name;
            document.getElementById('editEmployeePhone').value = employee.phone || '';
            document.getElementById('editEmployeeEmail').value = employee.email || '';

            // 设置技能复选框
            const skills = employee.skills || [];
            document.getElementById('editSkillManager').checked = skills.includes('manager');
            document.getElementById('editSkillSenior').checked = skills.includes('senior');
            document.getElementById('editSkillTrainee').checked = skills.includes('trainee');

            showModal('editEmployeeModal');
        }

        // 处理编辑员工
        function handleEditEmployee(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const employeeId = document.getElementById('editEmployeeId').value;
            const name = formData.get('name').trim();
            const phone = formData.get('phone').trim();
            const email = formData.get('email').trim();
            const skills = Array.from(formData.getAll('skills'));

            const errors = employeeManager.validateName(name, employees, employeeId);
            if (errors.length > 0) {
                showMessage(errors[0], 'error');
                return;
            }

            const employee = employees.find(emp => emp.id === employeeId);
            if (employee) {
                employee.name = name;
                employee.phone = phone;
                employee.email = email;
                employee.skills = skills;

                saveEmployees();
                renderEmployeesList();
                closeModals();
                showMessage('员工信息更新成功');
            }
        }

        // 切换员工状态
        function toggleEmployeeStatus(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) return;

            employee.is_active = !employee.is_active;
            saveEmployees();
            renderEmployeesList();
            showMessage(`员工${employee.is_active ? '启用' : '停用'}成功`);
        }

        // 删除员工
        function deleteEmployee(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) return;

            if (!confirm(`确定要删除员工"${employee.name}"吗？`)) return;

            const index = employees.findIndex(emp => emp.id === employeeId);
            if (index !== -1) {
                employees.splice(index, 1);
                saveEmployees();
                renderEmployeesList();
                showMessage('员工删除成功');
            }
        }

        // 生成排班
        function generateSchedule() {
            const activeEmployees = employees.filter(emp => emp.is_active);

            if (activeEmployees.length === 0) {
                showMessage('没有活跃的员工，请先添加并启用员工', 'error');
                return;
            }

            // 检查是否有已存在的排班数据
            const weekStart = getWeekStart(currentWeek);
            let existingSchedule = null;

            try {
                const history = scheduleAlgorithm.getScheduleHistory();
                existingSchedule = history.find(s => {
                    const historyWeekStart = new Date(s.week_start);
                    return historyWeekStart.toDateString() === weekStart.toDateString();
                });
            } catch (error) {
                console.warn('检查历史排班失败:', error);
            }

            // 生成排班（如果有已存在的数据，算法会保留过去的安排）
            currentSchedule = scheduleAlgorithm.generateSchedule(activeEmployees, currentWeek, existingSchedule);
            renderScheduleTable();
            switchTab('schedule');
            showMessage('排班生成成功');
        }

        // 获取周开始日期（周一）
        function getWeekStart(date) {
            const d = new Date(date);
            const day = d.getDay();
            const diff = d.getDate() - day + (day === 0 ? -6 : 1);
            return new Date(d.setDate(diff));
        }

        // 更新当前周显示
        function updateCurrentWeekDisplay() {
            const weekStart = getWeekStart(currentWeek);
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);

            elements.currentWeekSpan.textContent =
                `${weekStart.toLocaleDateString('zh-CN')} - ${weekEnd.toLocaleDateString('zh-CN')}`;
        }

        // 切换周
        function changeWeek(direction) {
            currentWeek.setDate(currentWeek.getDate() + (direction * 7));
            updateCurrentWeekDisplay();

            // 如果有排班数据，尝试加载历史记录或重新生成
            if (currentSchedule) {
                loadOrGenerateScheduleForCurrentWeek();
            }
        }

        // 为当前周加载或生成排班
        function loadOrGenerateScheduleForCurrentWeek() {
            const weekStart = getWeekStart(currentWeek);
            const weekStartISO = weekStart.toISOString();

            // 首先尝试从历史记录中加载
            try {
                const history = scheduleAlgorithm.getScheduleHistory();
                const existingSchedule = history.find(s => {
                    const historyWeekStart = new Date(s.week_start);
                    return historyWeekStart.toDateString() === weekStart.toDateString();
                });

                if (existingSchedule) {
                    // 找到历史记录，直接加载
                    console.log(`加载历史排班: ${weekStart.toDateString()}`);
                    currentSchedule = existingSchedule;
                    renderScheduleTable();
                    showMessage('已加载历史排班数据');
                    return;
                }
            } catch (error) {
                console.warn('加载历史排班失败:', error);
            }

            // 没有找到历史记录，重新生成
            console.log(`未找到历史排班，重新生成: ${weekStart.toDateString()}`);
            generateSchedule();
        }

        // 保存当前排班到历史记录
        function saveCurrentSchedule() {
            if (!currentSchedule) {
                console.warn('没有当前排班数据可保存');
                return;
            }

            try {
                // 更新生成时间
                currentSchedule.generated_at = new Date().toISOString();

                // 保存到历史记录
                scheduleAlgorithm.saveScheduleToHistory(currentSchedule);
                console.log('当前排班已保存到历史记录');
            } catch (error) {
                console.warn('保存当前排班失败:', error);
            }
        }

        // 渲染排班表
        function renderScheduleTable() {
            if (!currentSchedule) {
                elements.scheduleTable.innerHTML = '<div class="empty-state">请先生成排班表</div>';
                return;
            }

            const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            const shiftTypes = {
                'early': '早班(16:30)',
                'late': '晚班(20:30)',
                'off': '休息'
            };

            // 创建表格头部
            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>
                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                    <input type="checkbox" id="selectAllEmployeesTable" onchange="toggleSelectAllEmployeesInTable()" style="display: ${isDragMode ? 'none' : 'inline-block'};">
                                    员工
                                </div>
                            </th>
                            ${currentSchedule.days.map((day, index) => {
                                const date = new Date(day.date);
                                const isWeekend = day.weekday === 'sunday';
                                return `<th class="${isWeekend ? 'sunday' : ''}">
                                    <div>${date.getMonth() + 1}/${date.getDate()}</div>
                                    <div>${weekdays[index]}</div>
                                </th>`;
                            }).join('')}
                        </tr>
                    </thead>
                    <tbody>
            `;

            // 创建员工行
            const activeEmployees = employees.filter(emp => emp.is_active);
            activeEmployees.forEach(employee => {
                const isSelected = selectedEmployees.has(employee.id);

                // 检查员工当前是否在请假（今天）
                const today = new Date();
                const currentLeaves = getCurrentLeaves(employee.leaves || [], today);
                const isOnLeave = currentLeaves.length > 0;

                tableHTML += `<tr>
                    <td class="employee-name">
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <input type="checkbox" class="employee-checkbox"
                                   data-employee-id="${employee.id}"
                                   ${isSelected ? 'checked' : ''}
                                   onchange="toggleEmployeeSelection('${employee.id}')"
                                   style="display: ${isDragMode ? 'none' : 'inline-block'};">
                            ${employee.name}
                            ${isOnLeave ? '<span style="color: #856404; font-size: 0.8rem; margin-left: 0.5rem;">(今日请假)</span>' : ''}
                        </div>
                    </td>
                    ${currentSchedule.days.map((day, dayIndex) => {
                        const shift = day.assignments[employee.id] || 'off';
                        const isWeekend = day.weekday === 'sunday';

                        // 检查当天是否在请假期间
                        const dayDate = new Date(day.date);
                        const isDayOnLeave = scheduleAlgorithm.isEmployeeOnLeaveOnDate(employee.leaves || [], dayDate);

                        if (isWeekend) {
                            return `<td class="sunday">休息</td>`;
                        } else if (isDayOnLeave || shift === 'leave') {
                            return `<td style="background-color: #fff3cd; color: #856404; text-align: center; font-weight: 500;">
                                请假
                            </td>`;
                        } else {
                            const cellId = `cell-${employee.id}-${dayIndex}`;
                            return `<td id="${cellId}"
                                       data-employee-id="${employee.id}"
                                       data-day-index="${dayIndex}"
                                       ${isDragMode ? `
                                           draggable="true"
                                           ondragstart="handleDragStart(event)"
                                           ondragover="handleDragOver(event)"
                                           ondrop="handleDrop(event)"
                                           ondragend="handleDragEnd(event)"
                                       ` : ''}>
                                <select class="shift-select shift-${shift}"
                                        onchange="updateShift('${employee.id}', ${dayIndex}, this.value)"
                                        ${isDragMode ? 'style="pointer-events: none;"' : ''}>
                                    <option value="early" ${shift === 'early' ? 'selected' : ''}>早班(16:30)</option>
                                    <option value="late" ${shift === 'late' ? 'selected' : ''}>晚班(20:30)</option>
                                </select>
                            </td>`;
                        }
                    }).join('')}
                </tr>`;
            });

            tableHTML += '</tbody></table>';
            elements.scheduleTable.innerHTML = tableHTML;

            // 检查冲突
            if (currentSchedule) {
                checkConflicts();
            }
        }

        // 更新班次
        function updateShift(employeeId, dayIndex, newShift) {
            if (!currentSchedule) return;

            const day = currentSchedule.days[dayIndex];
            const currentShift = day.assignments[employeeId];

            // 检查是否是请假状态，请假状态不能修改
            if (currentShift === 'leave') {
                showMessage('请假期间不能修改班次，请先取消请假', 'error');
                renderScheduleTable();
                return;
            }

            // 检查是否尝试在工作日设置休息
            if (day.weekday !== 'sunday' && newShift === 'off') {
                showMessage('工作日(周一到周六)不能设置休息', 'error');
                // 恢复原来的值
                renderScheduleTable();
                return;
            }

            day.assignments[employeeId] = newShift;

            // 更新选择框样式
            const selectElement = event.target;
            selectElement.className = `shift-select shift-${newShift}`;

            // 保存修改后的排班到历史记录
            saveCurrentSchedule();

            showMessage('班次更新成功并已保存');

            // 重新检查冲突
            checkConflicts();
        }

        // 加载统计数据
        function loadStats() {
            if (!currentSchedule) {
                elements.validationResult.innerHTML = '<div class="empty-state">请先生成排班表</div>';
                elements.statsTable.innerHTML = '';
                return;
            }

            // 获取验证结果
            const validation = scheduleAlgorithm.validateSchedule(currentSchedule, employees);

            // 获取统计数据
            const stats = scheduleAlgorithm.calculateStats(currentSchedule, employees);

            renderValidationResult(validation);
            renderStatsTable(stats);
        }

        // 渲染验证结果
        function renderValidationResult(validation) {
            let resultHTML = '';

            // 主要验证结果
            resultHTML += `
                <div class="validation-item ${validation.is_valid ? 'validation-success' : 'validation-error'}">
                    <i class="fas ${validation.is_valid ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                    ${validation.is_valid ? '排班验证通过' : '排班验证失败'}
                </div>
            `;

            // 错误信息
            validation.errors.forEach(error => {
                resultHTML += `
                    <div class="validation-item validation-error">
                        <i class="fas fa-times-circle"></i>
                        ${error}
                    </div>
                `;
            });

            // 警告信息
            validation.warnings.forEach(warning => {
                resultHTML += `
                    <div class="validation-item validation-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        ${warning}
                    </div>
                `;
            });

            elements.validationResult.innerHTML = resultHTML;
        }

        // 渲染统计表格
        function renderStatsTable(stats) {
            if (stats.length === 0) {
                elements.statsTable.innerHTML = '<div class="empty-state">暂无统计数据</div>';
                return;
            }

            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>员工姓名</th>
                            <th>早班(16:30)</th>
                            <th>晚班(20:30)</th>
                            <th>休息天数</th>
                            <th>周日休息</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            stats.forEach(stat => {
                tableHTML += `
                    <tr>
                        <td>${stat.employee_name}</td>
                        <td><span class="stats-badge early">${stat.early_shifts}</span></td>
                        <td><span class="stats-badge late">${stat.late_shifts}</span></td>
                        <td><span class="stats-badge off">${stat.off_days}</span></td>
                        <td>
                            <i class="fas ${stat.sunday_off ? 'fa-check-circle' : 'fa-times-circle'}"
                               style="color: ${stat.sunday_off ? '#27ae60' : '#e74c3c'}"></i>
                        </td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';

            // 添加汇总信息
            const totalEarly = stats.reduce((sum, stat) => sum + stat.early_shifts, 0);
            const totalLate = stats.reduce((sum, stat) => sum + stat.late_shifts, 0);
            const sundayOffRate = Math.round((stats.filter(s => s.sunday_off).length / stats.length) * 100);

            tableHTML += `
                <div style="margin-top: 2rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="background: #e3f2fd; padding: 1rem; border-radius: 6px; text-align: center;">
                        <div style="color: #1976d2; font-size: 0.9rem; margin-bottom: 0.5rem;">总早班数</div>
                        <div style="color: #1976d2; font-size: 1.5rem; font-weight: bold;">${totalEarly}</div>
                    </div>
                    <div style="background: #fff3e0; padding: 1rem; border-radius: 6px; text-align: center;">
                        <div style="color: #f57c00; font-size: 0.9rem; margin-bottom: 0.5rem;">总晚班数</div>
                        <div style="color: #f57c00; font-size: 1.5rem; font-weight: bold;">${totalLate}</div>
                    </div>
                    <div style="background: #e8f5e8; padding: 1rem; border-radius: 6px; text-align: center;">
                        <div style="color: #27ae60; font-size: 0.9rem; margin-bottom: 0.5rem;">周日休息率</div>
                        <div style="color: #27ae60; font-size: 1.5rem; font-weight: bold;">${sundayOffRate}%</div>
                    </div>
                </div>
            `;

            elements.statsTable.innerHTML = tableHTML;
        }

        // ==================== 数据管理功能 ====================

        // 导出数据
        function exportData() {
            try {
                const data = dataManager.exportAllData();
                showMessage('数据导出成功');
            } catch (error) {
                showMessage('导出失败: ' + error.message, 'error');
            }
        }

        // 导入数据
        async function importData(file) {
            if (!file) return;

            try {
                const data = await dataManager.importData(file);

                // 重新加载页面数据
                loadEmployees();
                refreshStorageUsage();
                refreshScheduleHistory();

                showMessage('数据导入成功');

                // 清空文件输入
                elements.importFile.value = '';
            } catch (error) {
                showMessage('导入失败: ' + error.message, 'error');
                elements.importFile.value = '';
            }
        }

        // 创建备份
        function createBackup() {
            try {
                const backup = dataManager.createBackup();
                showMessage('备份创建成功');
                refreshStorageUsage();
            } catch (error) {
                showMessage('创建备份失败: ' + error.message, 'error');
            }
        }

        // 恢复备份
        function restoreBackup() {
            if (!confirm('确定要恢复备份吗？这将覆盖当前所有数据！')) {
                return;
            }

            try {
                const backup = dataManager.restoreBackup();

                // 重新加载页面数据
                loadEmployees();
                refreshStorageUsage();
                refreshScheduleHistory();

                showMessage('备份恢复成功');
            } catch (error) {
                showMessage('恢复备份失败: ' + error.message, 'error');
            }
        }

        // 清除排班历史
        function clearScheduleHistory() {
            if (!confirm('确定要清除所有排班历史吗？此操作不可恢复！')) {
                return;
            }

            try {
                localStorage.removeItem(dataManager.STORAGE_KEYS.schedules);
                refreshStorageUsage();
                refreshScheduleHistory();
                showMessage('排班历史已清除');
            } catch (error) {
                showMessage('清除失败: ' + error.message, 'error');
            }
        }

        // 清除所有数据
        function clearAllData() {
            if (!confirm('确定要清除所有数据吗？此操作不可恢复！\n\n这将删除：\n- 所有员工信息\n- 所有排班历史\n- 所有设置和备份')) {
                return;
            }

            try {
                dataManager.clearAllData();

                // 重新加载默认数据
                employees = employeeManager.getDefaultEmployees();
                employeeManager.saveEmployees(employees);
                renderEmployeesList();

                currentSchedule = null;
                renderScheduleTable();

                refreshStorageUsage();
                refreshScheduleHistory();

                showMessage('所有数据已清除，已恢复默认设置');
            } catch (error) {
                showMessage('清除失败: ' + error.message, 'error');
            }
        }

        // 刷新存储使用情况
        function refreshStorageUsage() {
            try {
                const usage = dataManager.getStorageUsage();
                let html = '';

                Object.entries(usage).forEach(([key, data]) => {
                    if (key !== 'total') {
                        const percentage = usage.total.size > 0 ? (data.size / usage.total.size * 100) : 0;
                        html += `
                            <div class="storage-item">
                                <span>${getStorageDisplayName(key)}</span>
                                <div style="display: flex; align-items: center;">
                                    <span>${data.sizeFormatted}</span>
                                    <div class="storage-bar">
                                        <div class="storage-bar-fill" style="width: ${percentage}%"></div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                });

                html += `
                    <div class="storage-item">
                        <span>总计</span>
                        <span>${usage.total.sizeFormatted}</span>
                    </div>
                `;

                elements.storageUsage.innerHTML = html;
            } catch (error) {
                elements.storageUsage.innerHTML = '<div class="empty-state">获取存储信息失败</div>';
            }
        }

        // 获取存储项显示名称
        function getStorageDisplayName(key) {
            const names = {
                employees: '员工数据',
                schedules: '排班历史',
                settings: '系统设置',
                backup: '备份数据'
            };
            return names[key] || key;
        }

        // 刷新排班历史
        function refreshScheduleHistory() {
            try {
                const history = scheduleAlgorithm.getScheduleHistory();

                if (history.length === 0) {
                    elements.scheduleHistory.innerHTML = '<div class="empty-state">暂无排班历史</div>';
                    return;
                }

                let html = '';
                history.forEach(schedule => {
                    const weekStart = new Date(schedule.week_start);
                    const weekEnd = new Date(weekStart);
                    weekEnd.setDate(weekEnd.getDate() + 6);

                    const generatedAt = schedule.generated_at ?
                        new Date(schedule.generated_at).toLocaleString('zh-CN') : '未知';

                    html += `
                        <div class="history-item">
                            <div class="history-info">
                                <div class="history-date">
                                    ${weekStart.toLocaleDateString('zh-CN')} - ${weekEnd.toLocaleDateString('zh-CN')}
                                </div>
                                <div class="history-meta">
                                    生成时间: ${generatedAt}
                                </div>
                            </div>
                            <div class="history-actions">
                                <button class="btn btn-sm btn-primary" onclick="loadHistorySchedule('${schedule.week_start}')">
                                    <i class="fas fa-eye"></i>
                                    查看
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteHistorySchedule('${schedule.week_start}')">
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            </div>
                        </div>
                    `;
                });

                elements.scheduleHistory.innerHTML = html;
            } catch (error) {
                elements.scheduleHistory.innerHTML = '<div class="empty-state">获取历史记录失败</div>';
            }
        }

        // 加载历史排班
        function loadHistorySchedule(weekStart) {
            try {
                const history = scheduleAlgorithm.getScheduleHistory();
                const schedule = history.find(s => s.week_start === weekStart);

                if (schedule) {
                    currentSchedule = schedule;
                    currentWeek = new Date(weekStart);
                    updateCurrentWeekDisplay();
                    renderScheduleTable();
                    switchTab('schedule');
                    showMessage('历史排班加载成功');
                } else {
                    showMessage('未找到指定的历史排班', 'error');
                }
            } catch (error) {
                showMessage('加载历史排班失败: ' + error.message, 'error');
            }
        }

        // 删除历史排班
        function deleteHistorySchedule(weekStart) {
            if (!confirm('确定要删除这个历史排班吗？')) {
                return;
            }

            try {
                const history = scheduleAlgorithm.getScheduleHistory();
                const filteredHistory = history.filter(s => s.week_start !== weekStart);

                localStorage.setItem(scheduleAlgorithm.HISTORY_STORAGE_KEY, JSON.stringify(filteredHistory));

                refreshScheduleHistory();
                refreshStorageUsage();
                showMessage('历史排班删除成功');
            } catch (error) {
                showMessage('删除失败: ' + error.message, 'error');
            }
        }

        // ==================== 用户体验增强功能 ====================

        // 切换拖拽模式
        function toggleDragMode() {
            isDragMode = !isDragMode;
            elements.dragModeText.textContent = isDragMode ? '禁用拖拽' : '启用拖拽';

            const dragBtn = elements.dragModeText.parentElement;
            dragBtn.classList.toggle('active', isDragMode);

            // 重新渲染表格以应用拖拽属性
            renderScheduleTable();

            showMessage(isDragMode ? '拖拽模式已启用' : '拖拽模式已禁用');
        }

        // 拖拽开始
        function handleDragStart(event) {
            if (!isDragMode) return;

            draggedElement = event.target.closest('td');
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/html', draggedElement.outerHTML);

            draggedElement.classList.add('dragging');
        }

        // 拖拽悬停
        function handleDragOver(event) {
            if (!isDragMode) return;

            event.preventDefault();
            event.dataTransfer.dropEffect = 'move';

            const targetCell = event.target.closest('td');
            if (targetCell && targetCell !== draggedElement) {
                targetCell.classList.add('drop-zone');
            }
        }

        // 拖拽放置
        function handleDrop(event) {
            if (!isDragMode) return;

            event.preventDefault();

            const targetCell = event.target.closest('td');
            if (!targetCell || targetCell === draggedElement) return;

            const sourceEmpId = draggedElement.dataset.employeeId;
            const sourceDayIndex = parseInt(draggedElement.dataset.dayIndex);
            const targetEmpId = targetCell.dataset.employeeId;
            const targetDayIndex = parseInt(targetCell.dataset.dayIndex);

            if (sourceEmpId && sourceDayIndex !== undefined && targetEmpId && targetDayIndex !== undefined) {
                // 交换班次
                const sourceShift = currentSchedule.days[sourceDayIndex].assignments[sourceEmpId];
                const targetShift = currentSchedule.days[targetDayIndex].assignments[targetEmpId];

                currentSchedule.days[sourceDayIndex].assignments[sourceEmpId] = targetShift;
                currentSchedule.days[targetDayIndex].assignments[targetEmpId] = sourceShift;

                renderScheduleTable();

                // 保存修改后的排班到历史记录
                saveCurrentSchedule();

                showMessage('班次交换成功并已保存');
            }
        }

        // 拖拽结束
        function handleDragEnd(event) {
            if (!isDragMode) return;

            // 清理样式
            document.querySelectorAll('.dragging').forEach(el => el.classList.remove('dragging'));
            document.querySelectorAll('.drop-zone').forEach(el => el.classList.remove('drop-zone'));

            draggedElement = null;
        }

        // 员工选择相关功能
        function toggleEmployeeSelection(employeeId) {
            if (selectedEmployees.has(employeeId)) {
                selectedEmployees.delete(employeeId);
            } else {
                selectedEmployees.add(employeeId);
            }

            updateBatchOperationsVisibility();
        }

        function toggleSelectAllEmployees() {
            const checkbox = document.getElementById('selectAllEmployees');
            const activeEmployees = employees.filter(emp => emp.is_active);

            if (checkbox.checked) {
                activeEmployees.forEach(emp => selectedEmployees.add(emp.id));
            } else {
                selectedEmployees.clear();
            }

            renderScheduleTable();
            updateBatchOperationsVisibility();
        }

        function toggleSelectAllEmployeesInTable() {
            const checkbox = document.getElementById('selectAllEmployeesTable');
            const activeEmployees = employees.filter(emp => emp.is_active);

            if (checkbox.checked) {
                activeEmployees.forEach(emp => selectedEmployees.add(emp.id));
            } else {
                selectedEmployees.clear();
            }

            // 更新表格中的复选框
            document.querySelectorAll('.employee-checkbox').forEach(cb => {
                cb.checked = selectedEmployees.has(cb.dataset.employeeId);
            });

            updateBatchOperationsVisibility();
        }

        function updateBatchOperationsVisibility() {
            const hasSeletion = selectedEmployees.size > 0;
            elements.batchOperations.style.display = hasSeletion ? 'block' : 'none';

            // 更新全选复选框状态
            const allCheckbox = document.getElementById('selectAllEmployees');
            const tableCheckbox = document.getElementById('selectAllEmployeesTable');
            const activeEmployees = employees.filter(emp => emp.is_active);

            if (allCheckbox) {
                allCheckbox.checked = selectedEmployees.size === activeEmployees.length;
                allCheckbox.indeterminate = selectedEmployees.size > 0 && selectedEmployees.size < activeEmployees.length;
            }

            if (tableCheckbox) {
                tableCheckbox.checked = selectedEmployees.size === activeEmployees.length;
                tableCheckbox.indeterminate = selectedEmployees.size > 0 && selectedEmployees.size < activeEmployees.length;
            }
        }

        function hideBatchOperations() {
            selectedEmployees.clear();
            elements.batchOperations.style.display = 'none';
            renderScheduleTable();
        }

        function applyBatchOperation() {
            if (selectedEmployees.size === 0) {
                showMessage('请先选择员工', 'warning');
                return;
            }

            const shiftType = document.getElementById('batchShiftType').value;
            const dayType = document.getElementById('batchDayType').value;

            // 检查是否尝试在工作日设置休息
            if (shiftType === 'off') {
                showMessage('工作日(周一到周六)不能设置休息，请选择早班或晚班', 'error');
                return;
            }

            let modifiedCount = 0;

            currentSchedule.days.forEach((day, dayIndex) => {
                // 跳过周日
                if (day.weekday === 'sunday') return;

                // 检查是否是目标日期
                if (dayType !== 'all' && day.weekday !== dayType) return;

                selectedEmployees.forEach(empId => {
                    if (day.assignments[empId] !== undefined) {
                        day.assignments[empId] = shiftType;
                        modifiedCount++;
                    }
                });
            });

            renderScheduleTable();

            // 保存修改后的排班到历史记录
            if (modifiedCount > 0) {
                saveCurrentSchedule();
            }

            hideBatchOperations();
            showMessage(`批量操作完成，修改了 ${modifiedCount} 个班次并已保存`);
        }

        // 排班模板功能
        function showTemplateModal() {
            if (!currentSchedule) {
                showMessage('请先生成排班表', 'warning');
                return;
            }
            showModal('saveTemplateModal');
        }

        function showLoadTemplateModal() {
            loadTemplateList();
            showModal('loadTemplateModal');
        }

        function handleSaveTemplate(event) {
            event.preventDefault();

            if (!currentSchedule) {
                showMessage('没有可保存的排班表', 'error');
                return;
            }

            const formData = new FormData(event.target);
            const name = formData.get('name').trim();
            const description = formData.get('description').trim();

            if (!name) {
                showMessage('请输入模板名称', 'error');
                return;
            }

            try {
                const templates = getTemplates();
                const template = {
                    id: Date.now().toString(),
                    name: name,
                    description: description,
                    schedule: JSON.parse(JSON.stringify(currentSchedule)),
                    createdAt: new Date().toISOString(),
                    employeeCount: employees.filter(emp => emp.is_active).length
                };

                templates.push(template);
                localStorage.setItem('paiban_templates', JSON.stringify(templates));

                closeModals();
                showMessage('模板保存成功');
            } catch (error) {
                showMessage('保存模板失败: ' + error.message, 'error');
            }
        }

        function getTemplates() {
            try {
                const stored = localStorage.getItem('paiban_templates');
                return stored ? JSON.parse(stored) : [];
            } catch (error) {
                console.error('读取模板失败:', error);
                return [];
            }
        }

        function loadTemplateList() {
            const templates = getTemplates();

            if (templates.length === 0) {
                elements.templateList.style.display = 'none';
                document.getElementById('noTemplates').style.display = 'block';
                return;
            }

            elements.templateList.style.display = 'grid';
            document.getElementById('noTemplates').style.display = 'none';

            elements.templateList.innerHTML = templates.map(template => `
                <div class="template-item">
                    <div class="template-name">${template.name}</div>
                    <div class="template-description">${template.description || '无描述'}</div>
                    <div class="template-meta" style="font-size: 0.8rem; color: #666; margin-bottom: 1rem;">
                        创建时间: ${new Date(template.createdAt).toLocaleDateString('zh-CN')}<br>
                        员工数量: ${template.employeeCount}人
                    </div>
                    <div class="template-actions">
                        <button class="btn btn-sm btn-primary" onclick="applyTemplate('${template.id}')">
                            <i class="fas fa-check"></i>
                            应用
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteTemplate('${template.id}')">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function applyTemplate(templateId) {
            try {
                const templates = getTemplates();
                const template = templates.find(t => t.id === templateId);

                if (!template) {
                    showMessage('模板不存在', 'error');
                    return;
                }

                const activeEmployees = employees.filter(emp => emp.is_active);
                if (activeEmployees.length !== template.employeeCount) {
                    if (!confirm(`当前活跃员工数量(${activeEmployees.length})与模板员工数量(${template.employeeCount})不匹配，是否继续应用？`)) {
                        return;
                    }
                }

                // 应用模板到当前周
                const templateSchedule = template.schedule;
                const newSchedule = {
                    week_start: currentWeek.toISOString(),
                    days: [],
                    generated_at: new Date().toISOString()
                };

                // 重新映射员工ID
                const employeeMapping = {};
                const templateEmployees = Object.keys(templateSchedule.days[0].assignments);
                activeEmployees.forEach((emp, index) => {
                    if (templateEmployees[index]) {
                        employeeMapping[templateEmployees[index]] = emp.id;
                    }
                });

                templateSchedule.days.forEach((templateDay, dayIndex) => {
                    const date = new Date(currentWeek);
                    date.setDate(date.getDate() + dayIndex);

                    const assignments = {};
                    activeEmployees.forEach(emp => {
                        assignments[emp.id] = 'off'; // 默认休息
                    });

                    // 应用模板分配
                    Object.entries(templateDay.assignments).forEach(([templateEmpId, shift]) => {
                        const mappedEmpId = employeeMapping[templateEmpId];
                        if (mappedEmpId) {
                            assignments[mappedEmpId] = shift;
                        }
                    });

                    newSchedule.days.push({
                        date: date.toISOString(),
                        weekday: templateDay.weekday,
                        assignments: assignments
                    });
                });

                currentSchedule = newSchedule;
                renderScheduleTable();
                closeModals();
                showMessage('模板应用成功');

            } catch (error) {
                showMessage('应用模板失败: ' + error.message, 'error');
            }
        }

        function deleteTemplate(templateId) {
            if (!confirm('确定要删除这个模板吗？')) {
                return;
            }

            try {
                const templates = getTemplates();
                const filteredTemplates = templates.filter(t => t.id !== templateId);
                localStorage.setItem('paiban_templates', JSON.stringify(filteredTemplates));

                loadTemplateList();
                showMessage('模板删除成功');
            } catch (error) {
                showMessage('删除模板失败: ' + error.message, 'error');
            }
        }

        // 冲突检测功能
        function checkConflicts() {
            if (!currentSchedule) return;

            const conflicts = [];
            const warnings = [];

            // 检查每日人员配置（周一到周六）
            currentSchedule.days.forEach((day, dayIndex) => {
                if (day.weekday === 'sunday') return;

                const dayName = ['周一', '周二', '周三', '周四', '周五', '周六'][dayIndex];
                const assignments = Object.values(day.assignments);
                const earlyCount = assignments.filter(shift => shift === 'early').length;
                const lateCount = assignments.filter(shift => shift === 'late').length;
                const offCount = assignments.filter(shift => shift === 'off').length;
                const leaveCount = assignments.filter(shift => shift === 'leave').length;

                if (earlyCount === 0) {
                    conflicts.push(`${dayName} 没有早班人员`);
                }
                if (lateCount === 0) {
                    conflicts.push(`${dayName} 没有晚班人员`);
                }
                if (offCount > 0) {
                    conflicts.push(`${dayName} 有${offCount}人休息，工作日不应该休息`);
                }

                const totalWorking = earlyCount + lateCount;
                const activeEmployeeCount = employees.filter(emp => emp.is_active).length;
                const expectedWorking = activeEmployeeCount - leaveCount; // 减去请假人数

                if (totalWorking !== expectedWorking) {
                    conflicts.push(`${dayName} 工作人员数量错误 (${totalWorking}/${expectedWorking}，${leaveCount}人请假)`);
                }

                // 检查早晚班平衡
                const idealEarlyCount = Math.ceil(activeEmployeeCount / 2);
                const earlyDiff = Math.abs(earlyCount - idealEarlyCount);
                if (earlyDiff > 1) {
                    warnings.push(`${dayName} 早晚班分配不平衡 (早班${earlyCount}人)`);
                }
            });

            // 检查员工早晚班总体平衡
            const activeEmployees = employees.filter(emp => emp.is_active);
            activeEmployees.forEach(emp => {
                let earlyShifts = 0;
                let lateShifts = 0;
                let workDayOffs = 0;

                currentSchedule.days.forEach(day => {
                    if (day.weekday === 'sunday') return;

                    const shift = day.assignments[emp.id];
                    if (shift === 'early') {
                        earlyShifts++;
                    } else if (shift === 'late') {
                        lateShifts++;
                    } else if (shift === 'off') {
                        workDayOffs++;
                    } else if (shift === 'leave') {
                        // 请假不计入工作日休息
                    }
                });

                if (workDayOffs > 0) {
                    conflicts.push(`${emp.name} 工作日有${workDayOffs}天休息`);
                }

                const shiftDiff = Math.abs(earlyShifts - lateShifts);
                if (shiftDiff > 1) {
                    warnings.push(`${emp.name} 早晚班不平衡 (早班${earlyShifts}次，晚班${lateShifts}次)`);
                }
            });

            // 显示冲突和警告
            displayConflictAlerts(conflicts, warnings);

            // 高亮有问题的单元格
            highlightConflictCells(conflicts, warnings);
        }

        function displayConflictAlerts(conflicts, warnings) {
            let alertHTML = '';

            if (conflicts.length > 0) {
                alertHTML += `
                    <div class="conflict-error">
                        <strong><i class="fas fa-exclamation-triangle"></i> 发现冲突：</strong>
                        <ul style="margin: 0.5rem 0 0 1.5rem;">
                            ${conflicts.map(conflict => `<li>${conflict}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            if (warnings.length > 0) {
                alertHTML += `
                    <div class="conflict-warning">
                        <strong><i class="fas fa-exclamation-circle"></i> 警告：</strong>
                        <ul style="margin: 0.5rem 0 0 1.5rem;">
                            ${warnings.map(warning => `<li>${warning}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }

            if (conflicts.length === 0 && warnings.length === 0) {
                alertHTML = `
                    <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 0.75rem; color: #155724;">
                        <i class="fas fa-check-circle"></i> 排班检查通过，未发现问题
                    </div>
                `;
            }

            elements.conflictAlerts.innerHTML = alertHTML;
        }

        function highlightConflictCells(conflicts, warnings) {
            // 清除之前的高亮
            document.querySelectorAll('.shift-conflict, .shift-error').forEach(el => {
                el.classList.remove('shift-conflict', 'shift-error');
            });

            // 这里可以根据具体冲突类型高亮相应的单元格
            // 由于冲突检测比较复杂，这里只做基础实现
        }

        // 自动平衡功能
        function autoBalance() {
            if (!currentSchedule) {
                showMessage('请先生成排班表', 'warning');
                return;
            }

            try {
                const activeEmployees = employees.filter(emp => emp.is_active);
                scheduleAlgorithm.optimizeShiftDistribution(currentSchedule, activeEmployees);
                scheduleAlgorithm.ensureWorkDayLimits(currentSchedule, activeEmployees);

                renderScheduleTable();

                // 保存修改后的排班到历史记录
                saveCurrentSchedule();

                showMessage('自动平衡完成并已保存');
            } catch (error) {
                showMessage('自动平衡失败: ' + error.message, 'error');
            }
        }

        // ==================== 扩展核心功能 ====================

        // 获取技能显示名称
        function getSkillDisplayName(skill) {
            const names = {
                manager: '管理员',
                senior: '资深员工',
                trainee: '实习生'
            };
            return names[skill] || skill;
        }

        // 获取当前请假记录
        function getCurrentLeaves(leaves, referenceDate = null) {
            const checkDate = referenceDate || new Date();
            checkDate.setHours(0, 0, 0, 0);

            return leaves.filter(leave => {
                const startDate = new Date(leave.startDate);
                const endDate = new Date(leave.endDate);
                startDate.setHours(0, 0, 0, 0);
                endDate.setHours(23, 59, 59, 999);

                return leave.status === 'approved' && checkDate >= startDate && checkDate <= endDate;
            });
        }

        // 检查员工在指定周是否请假
        function getEmployeeWeekLeaves(leaves, weekStart) {
            if (!leaves || leaves.length === 0) return [];

            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);

            return leaves.filter(leave => {
                if (leave.status !== 'approved') return false;

                const leaveStart = new Date(leave.startDate);
                const leaveEnd = new Date(leave.endDate);

                // 检查请假期间是否与排班周重叠
                return (leaveStart <= weekEnd && leaveEnd >= weekStart);
            });
        }

        // 更新员工统计
        function updateEmployeeStats() {
            const total = employees.length;
            const active = employees.filter(emp => emp.is_active).length;
            const onLeave = employees.filter(emp => getCurrentLeaves(emp.leaves || []).length > 0).length;

            document.getElementById('totalEmployees').textContent = total;
            document.getElementById('activeEmployees').textContent = active;
            document.getElementById('onLeaveEmployees').textContent = onLeave;
        }

        // 显示请假管理
        function showLeaveManagement(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) {
                showMessage('员工不存在', 'error');
                return;
            }

            // 更新模态框标题
            const modalTitle = elements.leaveManagementModal.querySelector('.modal-header h3');
            modalTitle.textContent = `请假管理 - ${employee.name}`;

            document.getElementById('leaveEmployeeId').value = employeeId;
            loadLeaveList(employeeId);
            showModal('leaveManagementModal');
        }

        // 加载请假列表
        function loadLeaveList(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee || !employee.leaves) {
                document.getElementById('leaveList').innerHTML = '<div class="empty-state">暂无请假记录</div>';
                return;
            }

            const leaves = employee.leaves.sort((a, b) => new Date(b.startDate) - new Date(a.startDate));

            if (leaves.length === 0) {
                document.getElementById('leaveList').innerHTML = '<div class="empty-state">暂无请假记录</div>';
                return;
            }

            document.getElementById('leaveList').innerHTML = leaves.map(leave => `
                <div class="leave-item">
                    <div class="leave-info">
                        <div class="leave-date">
                            ${new Date(leave.startDate).toLocaleDateString('zh-CN')} -
                            ${new Date(leave.endDate).toLocaleDateString('zh-CN')}
                        </div>
                        <div class="leave-reason">
                            ${getLeaveReasonDisplayName(leave.reason)}
                            ${leave.note ? ` - ${leave.note}` : ''}
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span class="leave-status ${leave.status}">${getLeaveStatusDisplayName(leave.status)}</span>
                        <button class="btn btn-sm btn-danger" onclick="deleteLeave('${employeeId}', '${leave.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 获取请假原因显示名称
        function getLeaveReasonDisplayName(reason) {
            const names = {
                sick: '病假',
                personal: '事假',
                annual: '年假',
                maternity: '产假',
                other: '其他'
            };
            return names[reason] || reason;
        }

        // 获取请假状态显示名称
        function getLeaveStatusDisplayName(status) {
            const names = {
                pending: '待审批',
                approved: '已批准',
                rejected: '已拒绝'
            };
            return names[status] || status;
        }

        // 添加请假记录
        function handleAddLeave(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const employeeId = document.getElementById('leaveEmployeeId').value;
            const startDate = formData.get('startDate');
            const endDate = formData.get('endDate');
            const reason = formData.get('reason');
            const note = formData.get('note').trim();

            if (!employeeId) {
                showMessage('请选择员工', 'error');
                return;
            }

            if (!startDate || !endDate) {
                showMessage('请选择开始和结束日期', 'error');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                showMessage('结束日期不能早于开始日期', 'error');
                return;
            }

            if (!reason) {
                showMessage('请选择请假原因', 'error');
                return;
            }

            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) {
                showMessage('员工不存在', 'error');
                return;
            }

            if (!employee.leaves) employee.leaves = [];

            const leave = {
                id: Date.now().toString(),
                startDate: startDate,
                endDate: endDate,
                reason: reason,
                note: note,
                status: 'approved', // 自动批准
                createdAt: new Date().toISOString()
            };

            employee.leaves.push(leave);
            saveEmployees();
            loadLeaveList(employeeId);
            renderEmployeesList();

            // 如果当前有排班表且请假日期与排班周重叠，重新生成排班表
            if (currentSchedule && shouldUpdateScheduleForLeave(leave, currentSchedule)) {
                try {
                    const weekStart = new Date(currentSchedule.week_start);
                    // 传入现有排班表，保持过去日期的安排不变
                    currentSchedule = scheduleAlgorithm.generateSchedule(employees, weekStart, currentSchedule);
                    renderScheduleTable();

                    // 保存更新后的排班到历史记录
                    saveCurrentSchedule();

                    showMessage('请假记录添加成功，排班表已更新并保存（保持过去日期不变）');
                } catch (error) {
                    showMessage('请假记录添加成功，但排班表更新失败: ' + error.message, 'warning');
                }
            } else {
                showMessage('请假记录添加成功');
            }

            // 清空表单
            e.target.reset();
        }

        // 删除请假记录
        function deleteLeave(employeeId, leaveId) {
            if (!confirm('确定要删除这条请假记录吗？')) return;

            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee || !employee.leaves) {
                console.error('员工或请假记录不存在:', { employeeId, employee });
                return;
            }

            // 保存被删除的请假记录信息，用于判断是否需要更新排班表
            const deletedLeave = employee.leaves.find(leave => leave.id === leaveId);
            console.log('删除请假记录:', {
                employeeId,
                leaveId,
                deletedLeave,
                currentSchedule: currentSchedule ? currentSchedule.week_start : null
            });

            if (!deletedLeave) {
                console.error('找不到要删除的请假记录:', leaveId);
                showMessage('找不到要删除的请假记录', 'error');
                return;
            }

            employee.leaves = employee.leaves.filter(leave => leave.id !== leaveId);
            saveEmployees();
            loadLeaveList(employeeId);
            renderEmployeesList();

            // 检查是否需要更新排班表
            const shouldUpdate = currentSchedule && shouldUpdateScheduleForLeave(deletedLeave, currentSchedule);
            console.log('是否需要更新排班表:', {
                hasCurrentSchedule: !!currentSchedule,
                shouldUpdate,
                deletedLeave
            });

            // 如果当前有排班表且删除的请假影响当前排班周，重新生成排班表
            if (shouldUpdate) {
                try {
                    const weekStart = new Date(currentSchedule.week_start);
                    console.log('重新生成排班表，周开始:', weekStart);
                    // 传入现有排班表，保持过去日期的安排不变
                    currentSchedule = scheduleAlgorithm.generateSchedule(employees, weekStart, currentSchedule);
                    renderScheduleTable();

                    // 保存更新后的排班到历史记录
                    saveCurrentSchedule();

                    showMessage('请假记录删除成功，排班表已更新并保存（保持过去日期不变）');
                } catch (error) {
                    console.error('排班表更新失败:', error);
                    showMessage('请假记录删除成功，但排班表更新失败: ' + error.message, 'warning');
                }
            } else {
                showMessage('请假记录删除成功');
            }
        }

        // 显示工时统计
        function showWorkHours(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) return;

            // 设置默认时间范围（当前月）
            const now = new Date();
            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

            document.getElementById('statsStartDate').value = startOfMonth.toISOString().split('T')[0];
            document.getElementById('statsEndDate').value = endOfMonth.toISOString().split('T')[0];

            showModal('workHoursModal');
            calculateWorkHours(employeeId);
        }

        // 计算工时
        function calculateWorkHours(employeeId = null) {
            const startDate = document.getElementById('statsStartDate').value;
            const endDate = document.getElementById('statsEndDate').value;

            if (!startDate || !endDate) {
                showMessage('请选择时间范围', 'warning');
                return;
            }

            const history = scheduleAlgorithm.getScheduleHistory();
            const targetEmployees = employeeId ? [employees.find(emp => emp.id === employeeId)] : employees.filter(emp => emp.is_active);

            const results = targetEmployees.map(emp => {
                let earlyShifts = 0;
                let lateShifts = 0;
                let totalWorkDays = 0;

                history.forEach(schedule => {
                    const scheduleDate = new Date(schedule.week_start);
                    if (scheduleDate >= new Date(startDate) && scheduleDate <= new Date(endDate)) {
                        schedule.days.forEach(day => {
                            if (day.weekday !== 'sunday') {
                                const shift = day.assignments[emp.id];
                                if (shift === 'early') {
                                    earlyShifts++;
                                    totalWorkDays++;
                                } else if (shift === 'late') {
                                    lateShifts++;
                                    totalWorkDays++;
                                }
                            }
                        });
                    }
                });

                // 假设早班8小时，晚班4小时
                const totalHours = earlyShifts * 8 + lateShifts * 4;

                return {
                    employee: emp,
                    earlyShifts,
                    lateShifts,
                    totalWorkDays,
                    totalHours
                };
            });

            displayWorkHoursResults(results);
        }

        // 显示工时统计结果
        function displayWorkHoursResults(results) {
            if (results.length === 0) {
                document.getElementById('workHoursResult').innerHTML = '<div class="empty-state">暂无数据</div>';
                return;
            }

            let html = `
                <div class="hours-summary">
                    <div class="hours-card">
                        <div class="hours-value">${results.reduce((sum, r) => sum + r.totalHours, 0)}</div>
                        <div class="hours-label">总工时</div>
                    </div>
                    <div class="hours-card">
                        <div class="hours-value">${results.reduce((sum, r) => sum + r.totalWorkDays, 0)}</div>
                        <div class="hours-label">总工作日</div>
                    </div>
                    <div class="hours-card">
                        <div class="hours-value">${results.reduce((sum, r) => sum + r.earlyShifts, 0)}</div>
                        <div class="hours-label">早班次数</div>
                    </div>
                    <div class="hours-card">
                        <div class="hours-value">${results.reduce((sum, r) => sum + r.lateShifts, 0)}</div>
                        <div class="hours-label">晚班次数</div>
                    </div>
                </div>
            `;

            if (results.length > 1) {
                html += `
                    <table style="width: 100%; border-collapse: collapse; margin-top: 1rem;">
                        <thead>
                            <tr style="background-color: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 0.5rem;">员工</th>
                                <th style="border: 1px solid #ddd; padding: 0.5rem;">早班</th>
                                <th style="border: 1px solid #ddd; padding: 0.5rem;">晚班</th>
                                <th style="border: 1px solid #ddd; padding: 0.5rem;">工作日</th>
                                <th style="border: 1px solid #ddd; padding: 0.5rem;">总工时</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${results.map(result => `
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 0.5rem;">${result.employee.name}</td>
                                    <td style="border: 1px solid #ddd; padding: 0.5rem; text-align: center;">${result.earlyShifts}</td>
                                    <td style="border: 1px solid #ddd; padding: 0.5rem; text-align: center;">${result.lateShifts}</td>
                                    <td style="border: 1px solid #ddd; padding: 0.5rem; text-align: center;">${result.totalWorkDays}</td>
                                    <td style="border: 1px solid #ddd; padding: 0.5rem; text-align: center;">${result.totalHours}h</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            }

            document.getElementById('workHoursResult').innerHTML = html;
        }

        // 检查请假是否影响当前排班表
        function shouldUpdateScheduleForLeave(leave, schedule) {
            if (!schedule || !leave) {
                console.log('shouldUpdateScheduleForLeave: 缺少参数', { schedule: !!schedule, leave: !!leave });
                return false;
            }

            const scheduleStart = new Date(schedule.week_start);
            const scheduleEnd = new Date(scheduleStart);
            scheduleEnd.setDate(scheduleEnd.getDate() + 6);

            const leaveStart = new Date(leave.startDate);
            const leaveEnd = new Date(leave.endDate);

            const overlaps = (leaveStart <= scheduleEnd && leaveEnd >= scheduleStart);

            console.log('shouldUpdateScheduleForLeave 检查:', {
                scheduleWeek: `${scheduleStart.toDateString()} - ${scheduleEnd.toDateString()}`,
                leavePeriod: `${leaveStart.toDateString()} - ${leaveEnd.toDateString()}`,
                overlaps
            });

            // 检查请假期间是否与排班周重叠
            return overlaps;
        }

        // 测试请假功能
        function testLeaveFunction() {
            console.log('=== 测试请假功能 ===');

            // 检查员工数据
            console.log('当前员工数据:', employees);

            // 为第一个员工添加测试请假记录
            if (employees.length > 0) {
                const testEmployee = employees[0];
                console.log('测试员工:', testEmployee);

                if (!testEmployee.leaves) testEmployee.leaves = [];

                // 添加一个明天的请假记录（只请假一天）
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);

                const testLeave = {
                    id: 'test-' + Date.now(),
                    startDate: tomorrow.toISOString().split('T')[0],
                    endDate: tomorrow.toISOString().split('T')[0], // 只请假一天
                    reason: 'sick',
                    note: '测试请假（只请假一天）',
                    status: 'approved',
                    createdAt: new Date().toISOString()
                };

                testEmployee.leaves.push(testLeave);
                console.log('添加测试请假记录:', testLeave);

                // 保存并刷新
                saveEmployees();
                renderEmployeesList();

                showMessage(`为 ${testEmployee.name} 添加了测试请假记录`);
            } else {
                showMessage('没有员工可以测试', 'error');
            }
        }

        // 强制更新排班表
        function forceUpdateSchedule() {
            if (!currentSchedule) {
                showMessage('当前没有排班表', 'warning');
                return;
            }

            try {
                const weekStart = new Date(currentSchedule.week_start);
                console.log('强制更新排班表，周开始:', weekStart);
                console.log('当前员工数据:', employees);

                // 传入现有排班表，保持过去日期的安排不变
                currentSchedule = scheduleAlgorithm.generateSchedule(employees, weekStart, currentSchedule);
                renderScheduleTable();
                showMessage('排班表强制更新成功（保持过去日期不变）');
            } catch (error) {
                console.error('强制更新排班表失败:', error);
                showMessage('强制更新排班表失败: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>

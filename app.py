from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime, timedelta
from schedule_algorithm import ScheduleAlgorithm
from employee_manager import EmployeeManager

app = Flask(__name__)
CORS(app)

# 初始化管理器
employee_manager = EmployeeManager()
schedule_algorithm = ScheduleAlgorithm()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/employees', methods=['GET'])
def get_employees():
    """获取所有员工"""
    return jsonify(employee_manager.get_all_employees())

@app.route('/api/employees', methods=['POST'])
def add_employee():
    """添加员工"""
    data = request.get_json()
    name = data.get('name', '').strip()
    
    if not name:
        return jsonify({'error': '员工姓名不能为空'}), 400
    
    try:
        employee = employee_manager.add_employee(name)
        return jsonify(employee)
    except ValueError as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/employees/<employee_id>', methods=['PUT'])
def update_employee(employee_id):
    """更新员工信息"""
    data = request.get_json()
    try:
        employee = employee_manager.update_employee(employee_id, data)
        return jsonify(employee)
    except ValueError as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/employees/<employee_id>', methods=['DELETE'])
def delete_employee(employee_id):
    """删除员工"""
    try:
        employee_manager.delete_employee(employee_id)
        return jsonify({'success': True})
    except ValueError as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/schedule/generate', methods=['POST'])
def generate_schedule():
    """生成排班表"""
    data = request.get_json()
    week_start = data.get('week_start')
    
    if not week_start:
        return jsonify({'error': '请提供周开始日期'}), 400
    
    try:
        week_start_date = datetime.fromisoformat(week_start.replace('Z', '+00:00'))
        employees = employee_manager.get_active_employees()
        
        if not employees:
            return jsonify({'error': '没有活跃的员工'}), 400
        
        schedule = schedule_algorithm.generate_schedule(employees, week_start_date)
        return jsonify(schedule)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/schedule/validate', methods=['POST'])
def validate_schedule():
    """验证排班表"""
    data = request.get_json()
    schedule = data.get('schedule')
    
    if not schedule:
        return jsonify({'error': '请提供排班数据'}), 400
    
    try:
        employees = employee_manager.get_all_employees()
        validation_result = schedule_algorithm.validate_schedule(schedule, employees)
        return jsonify(validation_result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/schedule/stats', methods=['POST'])
def get_schedule_stats():
    """获取排班统计"""
    data = request.get_json()
    schedule = data.get('schedule')
    
    if not schedule:
        return jsonify({'error': '请提供排班数据'}), 400
    
    try:
        employees = employee_manager.get_all_employees()
        stats = schedule_algorithm.calculate_stats(schedule, employees)
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

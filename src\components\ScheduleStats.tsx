import React from 'react';
import { BarChart3, CheckCircle, AlertCircle, XCircle } from 'lucide-react';
import { ScheduleStats, ValidationResult } from '../types';

interface ScheduleStatsProps {
  stats: ScheduleStats[];
  validation: ValidationResult;
}

export function ScheduleStatsComponent({ stats, validation }: ScheduleStatsProps) {
  if (stats.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center gap-2 mb-6">
        <BarChart3 className="text-blue-600" size={20} />
        <h2 className="text-xl font-semibold text-gray-800">排班统计</h2>
      </div>

      {/* 验证结果 */}
      <div className="mb-6">
        <div className={`flex items-center gap-2 p-3 rounded-md ${
          validation.isValid 
            ? 'bg-green-50 border border-green-200' 
            : 'bg-red-50 border border-red-200'
        }`}>
          {validation.isValid ? (
            <CheckCircle className="text-green-600" size={20} />
          ) : (
            <XCircle className="text-red-600" size={20} />
          )}
          <span className={`font-medium ${
            validation.isValid ? 'text-green-800' : 'text-red-800'
          }`}>
            {validation.isValid ? '排班验证通过' : '排班验证失败'}
          </span>
        </div>

        {/* 错误信息 */}
        {validation.errors.length > 0 && (
          <div className="mt-3 space-y-1">
            {validation.errors.map((error, index) => (
              <div key={index} className="flex items-center gap-2 text-red-600 text-sm">
                <XCircle size={16} />
                {error}
              </div>
            ))}
          </div>
        )}

        {/* 警告信息 */}
        {validation.warnings.length > 0 && (
          <div className="mt-3 space-y-1">
            {validation.warnings.map((warning, index) => (
              <div key={index} className="flex items-center gap-2 text-orange-600 text-sm">
                <AlertCircle size={16} />
                {warning}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 统计表格 */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-300 px-4 py-3 text-left font-medium text-gray-700">
                员工姓名
              </th>
              <th className="border border-gray-300 px-4 py-3 text-center font-medium text-gray-700">
                早班(16:30)
              </th>
              <th className="border border-gray-300 px-4 py-3 text-center font-medium text-gray-700">
                晚班(20:30)
              </th>
              <th className="border border-gray-300 px-4 py-3 text-center font-medium text-gray-700">
                休息天数
              </th>
              <th className="border border-gray-300 px-4 py-3 text-center font-medium text-gray-700">
                周日休息
              </th>
            </tr>
          </thead>
          <tbody>
            {stats.map((stat) => (
              <tr key={stat.employeeId}>
                <td className="border border-gray-300 px-4 py-3 font-medium text-gray-700">
                  {stat.employeeName}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center">
                  <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                    {stat.earlyShifts}
                  </span>
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center">
                  <span className="inline-block bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-sm">
                    {stat.lateShifts}
                  </span>
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center">
                  <span className="inline-block bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm">
                    {stat.offDays}
                  </span>
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center">
                  {stat.sundayOff ? (
                    <CheckCircle className="text-green-600 mx-auto" size={20} />
                  ) : (
                    <XCircle className="text-red-600 mx-auto" size={20} />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 汇总信息 */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 p-4 rounded-md">
          <div className="text-blue-600 text-sm font-medium">总早班数</div>
          <div className="text-2xl font-bold text-blue-800">
            {stats.reduce((sum, stat) => sum + stat.earlyShifts, 0)}
          </div>
        </div>
        <div className="bg-orange-50 p-4 rounded-md">
          <div className="text-orange-600 text-sm font-medium">总晚班数</div>
          <div className="text-2xl font-bold text-orange-800">
            {stats.reduce((sum, stat) => sum + stat.lateShifts, 0)}
          </div>
        </div>
        <div className="bg-green-50 p-4 rounded-md">
          <div className="text-green-600 text-sm font-medium">周日休息率</div>
          <div className="text-2xl font-bold text-green-800">
            {stats.length > 0 ? Math.round((stats.filter(s => s.sundayOff).length / stats.length) * 100) : 0}%
          </div>
        </div>
      </div>
    </div>
  );
}

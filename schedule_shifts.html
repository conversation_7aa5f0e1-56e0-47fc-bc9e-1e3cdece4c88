<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三岗位排班系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 导出功能依赖库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        .header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e0e0e0;
        }

        .header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 20px;
        }

        .title {
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .title i {
            margin-right: 0.5rem;
            color: #3498db;
        }

        /* 导航栏样式 */
        .nav {
            background: #fff;
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-tabs {
            display: flex;
            gap: 0;
        }

        .nav-tab {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            color: #666;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-tab:hover {
            color: #3498db;
            background-color: #f8f9fa;
        }

        .nav-tab.active {
            color: #3498db;
            border-bottom-color: #3498db;
            background-color: #f8f9fa;
        }

        /* 主内容区样式 */
        .main {
            padding: 2rem 0;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 卡片样式 */
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        /* 表格样式 */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        /* 岗位管理样式 */
        .department-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .department-card {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .department-header {
            padding: 1rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .department-title {
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .department-body {
            padding: 1rem;
        }

        .employee-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .employee-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .employee-item:last-child {
            border-bottom: none;
        }

        .employee-name {
            font-weight: 500;
        }

        .employee-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        /* 排班表样式 */
        .schedule-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .week-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            font-size: 0.9rem;
        }

        .schedule-table th,
        .schedule-table td {
            padding: 0.5rem;
            text-align: center;
            border: 1px solid #ddd;
            vertical-align: middle;
        }

        .schedule-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .schedule-table .employee-name-cell {
            text-align: left;
            font-weight: 500;
            background: #f8f9fa;
        }

        .schedule-table .department-header {
            background: #e9ecef;
            font-weight: 600;
            color: #495057;
        }

        .shift-select {
            width: 100%;
            padding: 0.25rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .shift-work {
            background: #d4edda;
            color: #155724;
        }

        .shift-rest {
            background: #f8d7da;
            color: #721c24;
        }

        .weekend {
            background: #fff3cd;
        }

        .workday {
            background: #d1ecf1;
        }

        /* 统计样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #fff;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        /* 消息提示样式 */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .message.show {
            transform: translateX(0);
        }

        .message.success {
            background: #27ae60;
        }

        .message.error {
            background: #e74c3c;
        }

        .message.warning {
            background: #f39c12;
        }

        .message.info {
            background: #3498db;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
        }

        .btn-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .btn-close:hover {
            color: #333;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        /* 导出菜单样式 */
        .btn-group {
            position: relative;
            display: inline-block;
        }

        .export-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 150px;
            margin-top: 2px;
        }

        .export-option {
            display: block;
            width: 100%;
            padding: 0.5rem 1rem;
            border: none;
            background: none;
            text-align: left;
            cursor: pointer;
            transition: background-color 0.2s;
            font-size: 0.9rem;
        }

        .export-option:hover {
            background-color: #f8f9fa;
        }

        .export-option i {
            margin-right: 0.5rem;
            width: 16px;
        }

        /* 导出时的样式优化 */
        .schedule-table-export {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }

        .schedule-table-export th,
        .schedule-table-export td {
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
            font-size: 12px;
        }

        .schedule-table-export th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        .schedule-table-export .employee-name-cell {
            text-align: left;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .schedule-table-export .department-header {
            background-color: #e9ecef;
            font-weight: bold;
            text-align: center;
        }

        .schedule-table-export .workday {
            background-color: #d1ecf1;
        }

        .schedule-table-export .weekend {
            background-color: #fff3cd;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .nav-tab {
                padding: 0.75rem 1rem;
                font-size: 0.8rem;
            }

            .schedule-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .schedule-table {
                font-size: 0.8rem;
            }

            .schedule-table th,
            .schedule-table td {
                padding: 0.25rem;
            }

            .department-grid {
                grid-template-columns: 1fr;
            }

            .export-menu {
                right: auto;
                left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                <i class="fas fa-calendar-alt"></i>
                三岗位排班系统
            </h1>
            <div>
                <span class="text-muted">电流线 | 电压线 | 资料组</span>
            </div>
        </div>
    </div>

    <div class="nav">
        <div class="container">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="switchTab('departments')">
                    <i class="fas fa-users"></i>
                    岗位管理
                </button>
                <button class="nav-tab" onclick="switchTab('schedule')">
                    <i class="fas fa-calendar-week"></i>
                    排班表
                </button>
                <button class="nav-tab" onclick="switchTab('stats')">
                    <i class="fas fa-chart-bar"></i>
                    统计分析
                </button>
                <button class="nav-tab" onclick="switchTab('data-management')">
                    <i class="fas fa-database"></i>
                    数据管理
                </button>
            </div>
        </div>
    </div>

    <div class="main">
        <div class="container">
            <!-- 岗位管理页面 -->
            <div id="departments" class="tab-content active">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-building"></i>
                            岗位人员管理
                        </h2>
                        <button class="btn btn-primary" onclick="showAddEmployeeModal()">
                            <i class="fas fa-plus"></i>
                            添加员工
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="department-grid" id="departmentGrid">
                            <!-- 岗位卡片将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 排班表页面 -->
            <div id="schedule" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-calendar-week"></i>
                            周排班表
                        </h2>
                        <div class="schedule-controls">
                            <div class="week-selector">
                                <button class="btn btn-secondary btn-sm" onclick="changeWeek(-1)">
                                    <i class="fas fa-chevron-left"></i>
                                    上周
                                </button>
                                <div style="display: flex; flex-direction: column; align-items: center; margin: 0 1rem;">
                                    <span id="currentWeekDisplay"></span>
                                    <small id="weekStatus" style="color: #666; font-size: 0.8rem;"></small>
                                </div>
                                <button class="btn btn-secondary btn-sm" onclick="changeWeek(1)">
                                    下周
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <button class="btn btn-success" onclick="generateSchedule()">
                                    <i class="fas fa-magic"></i>
                                    生成排班
                                </button>
                                <button class="btn btn-primary" onclick="showRotationInfo()" id="rotationInfoBtn" style="display: none;">
                                    <i class="fas fa-info-circle"></i>
                                    轮换信息
                                </button>
                                <div class="btn-group" style="position: relative;">
                                    <button class="btn btn-warning btn-sm" onclick="toggleExportMenu()" id="exportBtn" style="display: none;" title="导出排班表">
                                        <i class="fas fa-download"></i>
                                        导出
                                    </button>
                                    <div id="exportMenu" class="export-menu" style="display: none;">
                                        <button onclick="exportAsImage()" class="export-option">
                                            <i class="fas fa-image"></i>
                                            导出为图片
                                        </button>
                                        <button onclick="exportAsPDF()" class="export-option">
                                            <i class="fas fa-file-pdf"></i>
                                            导出为PDF
                                        </button>
                                    </div>
                                </div>
                                <button class="btn btn-secondary btn-sm" onclick="showWeekNavigation()" title="查看所有已保存的周次">
                                    <i class="fas fa-list"></i>
                                    周导航
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="resetSundayHistory()" title="重置周日分配历史，重新开始公平分配">
                                    <i class="fas fa-redo"></i>
                                    重置周日
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="scheduleTableContainer">
                            <!-- 排班表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计分析页面 -->
            <div id="stats" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-chart-bar"></i>
                            排班统计分析
                        </h2>
                        <button class="btn btn-primary" onclick="refreshStats()">
                            <i class="fas fa-sync"></i>
                            刷新统计
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="stats-grid" id="statsGrid">
                            <!-- 统计卡片将在这里动态生成 -->
                        </div>
                        <div id="detailedStats">
                            <!-- 详细统计表格将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据管理页面 -->
            <div id="data-management" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i class="fas fa-database"></i>
                            数据导入导出
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                            <!-- 导出数据 -->
                            <div class="export-section">
                                <h3 style="margin-bottom: 1rem; color: #2c3e50;">
                                    <i class="fas fa-download"></i>
                                    导出数据
                                </h3>
                                <p style="color: #666; margin-bottom: 1rem;">
                                    将当前的员工数据和排班表导出为JSON文件，可用于备份或迁移到其他设备。
                                </p>
                                <div style="margin-bottom: 1rem;">
                                    <div class="form-group">
                                        <label class="form-label">导出内容</label>
                                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                            <label style="display: flex; align-items: center; gap: 0.5rem; font-weight: normal;">
                                                <input type="checkbox" id="exportEmployees" checked>
                                                <span id="employeeDataLabel">员工数据</span>
                                            </label>
                                            <label style="display: flex; align-items: center; gap: 0.5rem; font-weight: normal;">
                                                <input type="checkbox" id="exportSchedule" checked>
                                                <span id="scheduleDataLabel">排班表数据</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-success" onclick="exportData()" style="width: 100%;">
                                    <i class="fas fa-download"></i>
                                    导出数据文件
                                </button>
                                <div id="exportStatus" style="margin-top: 1rem; font-size: 0.9rem; color: #666;"></div>
                            </div>

                            <!-- 导入数据 -->
                            <div class="import-section">
                                <h3 style="margin-bottom: 1rem; color: #2c3e50;">
                                    <i class="fas fa-upload"></i>
                                    导入数据
                                </h3>
                                <p style="color: #666; margin-bottom: 1rem;">
                                    从JSON文件导入员工数据和排班表，将覆盖当前数据。建议先导出备份。
                                </p>
                                <div style="margin-bottom: 1rem;">
                                    <div class="form-group">
                                        <label class="form-label">选择数据文件</label>
                                        <input type="file" class="form-control" id="importFile" accept=".json" onchange="handleFileSelect(event)">
                                        <small style="color: #666; margin-top: 0.5rem; display: block;">
                                            支持格式：JSON文件 (.json)
                                        </small>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="importData()" disabled id="importBtn" style="width: 100%;">
                                    <i class="fas fa-upload"></i>
                                    导入数据文件
                                </button>
                                <div id="importStatus" style="margin-top: 1rem; font-size: 0.9rem; color: #666;"></div>
                            </div>
                        </div>

                        <!-- 数据预览 -->
                        <div id="dataPreview" style="margin-top: 2rem; display: none;">
                            <h3 style="margin-bottom: 1rem; color: #2c3e50;">
                                <i class="fas fa-eye"></i>
                                数据预览
                            </h3>
                            <div class="card" style="background: #f8f9fa;">
                                <div class="card-body">
                                    <pre id="previewContent" style="background: white; padding: 1rem; border-radius: 4px; max-height: 300px; overflow-y: auto; font-size: 0.8rem;"></pre>
                                </div>
                            </div>
                        </div>

                        <!-- 操作说明 -->
                        <div style="margin-top: 2rem; padding: 1rem; background: #e3f2fd; border-radius: 4px;">
                            <h4 style="color: #1976d2; margin-bottom: 0.5rem;">
                                <i class="fas fa-info-circle"></i>
                                使用说明
                            </h4>
                            <ul style="margin: 0; padding-left: 1.5rem; color: #666;">
                                <li><strong>导出数据</strong>：点击导出按钮会下载一个JSON文件到您的下载文件夹</li>
                                <li><strong>导入数据</strong>：选择之前导出的JSON文件，点击导入按钮恢复数据</li>
                                <li><strong>数据备份</strong>：建议定期导出数据作为备份，防止数据丢失</li>
                                <li><strong>数据迁移</strong>：可以将导出的文件复制到其他设备上导入使用</li>
                                <li><strong>注意事项</strong>：导入数据会覆盖当前所有数据，请谨慎操作</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加员工模态框 -->
    <div id="addEmployeeModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加员工</h3>
                <button class="btn-close" onclick="hideAddEmployeeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addEmployeeForm">
                    <div class="form-group">
                        <label class="form-label">员工姓名</label>
                        <input type="text" class="form-control" id="employeeName" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">所属岗位</label>
                        <select class="form-control form-select" id="employeeDepartment" required>
                            <option value="">请选择岗位</option>
                            <option value="current">电流线</option>
                            <option value="voltage">电压线</option>
                            <option value="data">资料组</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select class="form-control form-select" id="employeeStatus">
                            <option value="true">启用</option>
                            <option value="false">停用</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideAddEmployeeModal()">取消</button>
                <button class="btn btn-primary" onclick="addEmployee()">添加</button>
            </div>
        </div>
    </div>

    <!-- 编辑员工模态框 -->
    <div id="editEmployeeModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑员工</h3>
                <button class="btn-close" onclick="hideEditEmployeeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editEmployeeForm">
                    <input type="hidden" id="editEmployeeId">
                    <div class="form-group">
                        <label class="form-label">员工姓名</label>
                        <input type="text" class="form-control" id="editEmployeeName" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">所属岗位</label>
                        <select class="form-control form-select" id="editEmployeeDepartment" required>
                            <option value="current">电流线</option>
                            <option value="voltage">电压线</option>
                            <option value="data">资料组</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select class="form-control form-select" id="editEmployeeStatus">
                            <option value="true">启用</option>
                            <option value="false">停用</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideEditEmployeeModal()">取消</button>
                <button class="btn btn-primary" onclick="updateEmployee()">保存</button>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message"></div>

    <script>
        // 全局变量
        let employees = [];
        let currentSchedule = null;
        let currentWeek = new Date();

        // 岗位配置
        const DEPARTMENTS = {
            current: {
                name: '电流线',
                icon: 'fas fa-bolt',
                color: '#e74c3c',
                maxEmployees: 6
            },
            voltage: {
                name: '电压线',
                icon: 'fas fa-plug',
                color: '#f39c12',
                maxEmployees: 5
            },
            data: {
                name: '资料组',
                icon: 'fas fa-file-alt',
                color: '#3498db',
                maxEmployees: 5
            }
        };

        // 工作日配置（周一和周六）
        const WORK_DAYS = [1, 6]; // 1=周一, 6=周六
        const REST_DAYS = [0, 2, 3, 4, 5]; // 0=周日, 2=周二, 3=周三, 4=周四, 5=周五

        // 员工管理类
        class EmployeeManager {
            constructor() {
                this.storageKey = 'shift_schedule_employees';
            }

            // 获取默认员工数据
            getDefaultEmployees() {
                return [
                    // 电流线 - 6人（丁磊已调到资料组）
                    { id: 'emp1', name: '周宏', department: 'current', is_active: true },
                    { id: 'emp2', name: '夏燚', department: 'current', is_active: true },
                    { id: 'emp3', name: '魏霞', department: 'current', is_active: true },
                    { id: 'emp4', name: '马景涛', department: 'current', is_active: true },
                    { id: 'emp5', name: '陈颂强', department: 'current', is_active: true },
                    { id: 'emp6', name: '周子翰', department: 'current', is_active: true },

                    // 电压线 - 5人
                    { id: 'emp7', name: '钱伟', department: 'voltage', is_active: true },
                    { id: 'emp8', name: '陈远睦', department: 'voltage', is_active: true },
                    { id: 'emp9', name: '林艳春', department: 'voltage', is_active: true },
                    { id: 'emp10', name: '沈玲枫', department: 'voltage', is_active: true },
                    { id: 'emp11', name: '赵玉林', department: 'voltage', is_active: true },

                    // 资料组 - 5人（包含丁磊）
                    { id: 'emp12', name: '陈燚', department: 'data', is_active: true },
                    { id: 'emp13', name: '马镜轩', department: 'data', is_active: true },
                    { id: 'emp14', name: '赵千禧', department: 'data', is_active: true },
                    { id: 'emp15', name: '唐秋菊', department: 'data', is_active: true },
                    { id: 'emp16', name: '丁磊', department: 'data', is_active: true }
                ];
            }

            // 加载员工数据
            loadEmployees() {
                try {
                    const stored = localStorage.getItem(this.storageKey);
                    if (stored) {
                        return JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载员工数据失败:', error);
                }
                return this.getDefaultEmployees();
            }

            // 保存员工数据
            saveEmployees(employees) {
                try {
                    localStorage.setItem(this.storageKey, JSON.stringify(employees));
                } catch (error) {
                    console.error('保存员工数据失败:', error);
                }
            }

            // 生成新的员工ID
            generateId() {
                return 'emp_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
            }

            // 验证员工姓名
            validateName(name, employees, excludeId = null) {
                const errors = [];

                if (!name || !name.trim()) {
                    errors.push('员工姓名不能为空');
                }

                if (name.trim().length > 20) {
                    errors.push('员工姓名不能超过20个字符');
                }

                const trimmedName = name.trim();
                const isDuplicate = employees.some(emp =>
                    emp.name === trimmedName && emp.id !== excludeId
                );

                if (isDuplicate) {
                    errors.push('员工姓名已存在');
                }

                return errors;
            }

            // 添加员工
            addEmployee(name, department, isActive = true) {
                const errors = this.validateName(name, employees);
                if (errors.length > 0) {
                    throw new Error(errors.join(', '));
                }

                // 检查岗位人数限制
                const deptEmployees = employees.filter(emp => emp.department === department);
                const maxEmployees = DEPARTMENTS[department].maxEmployees;
                if (deptEmployees.length >= maxEmployees) {
                    throw new Error(`${DEPARTMENTS[department].name}已达到最大人数限制(${maxEmployees}人)`);
                }

                const newEmployee = {
                    id: this.generateId(),
                    name: name.trim(),
                    department: department,
                    is_active: isActive
                };

                employees.push(newEmployee);
                this.saveEmployees(employees);
                return newEmployee;
            }

            // 更新员工
            updateEmployee(id, name, department, isActive) {
                const employee = employees.find(emp => emp.id === id);
                if (!employee) {
                    throw new Error('员工不存在');
                }

                const errors = this.validateName(name, employees, id);
                if (errors.length > 0) {
                    throw new Error(errors.join(', '));
                }

                // 如果更改了岗位，检查新岗位人数限制
                if (department !== employee.department) {
                    const deptEmployees = employees.filter(emp =>
                        emp.department === department && emp.id !== id
                    );
                    const maxEmployees = DEPARTMENTS[department].maxEmployees;
                    if (deptEmployees.length >= maxEmployees) {
                        throw new Error(`${DEPARTMENTS[department].name}已达到最大人数限制(${maxEmployees}人)`);
                    }
                }

                employee.name = name.trim();
                employee.department = department;
                employee.is_active = isActive;

                this.saveEmployees(employees);
                return employee;
            }

            // 删除员工
            deleteEmployee(id) {
                const index = employees.findIndex(emp => emp.id === id);
                if (index === -1) {
                    throw new Error('员工不存在');
                }

                employees.splice(index, 1);
                this.saveEmployees(employees);
            }

            // 切换员工状态
            toggleEmployeeStatus(id) {
                const employee = employees.find(emp => emp.id === id);
                if (!employee) {
                    throw new Error('员工不存在');
                }

                employee.is_active = !employee.is_active;
                this.saveEmployees(employees);
                return employee;
            }
        }

        // 排班算法类
        class ScheduleAlgorithm {
            constructor() {
                this.storageKey = 'shift_schedule_data';
                this.historyStorageKey = 'shift_schedule_history';
                this.maxHistoryWeeks = 10; // 保留最近10周的历史记录
            }

            // 生成排班表
            generateSchedule(employees, weekStart) {
                // 确保周开始是周一
                const week = new Date(weekStart);
                week.setDate(week.getDate() - week.getDay() + 1);

                // 只使用活跃的员工
                const activeEmployees = employees.filter(emp => emp.is_active);

                if (activeEmployees.length === 0) {
                    throw new Error('没有活跃的员工');
                }

                // 按岗位分组员工
                const employeesByDept = {
                    current: activeEmployees.filter(emp => emp.department === 'current'),
                    voltage: activeEmployees.filter(emp => emp.department === 'voltage'),
                    data: activeEmployees.filter(emp => emp.department === 'data')
                };

                // 获取历史排班记录
                const history = this.getScheduleHistory();

                // 使用改进的智能排班算法（支持跨周轮换）
                const schedule = this.generateSmartScheduleWithRotation(week, employeesByDept, history);

                // 保存到历史记录
                this.saveScheduleToHistory(schedule);

                return schedule;
            }

            // 获取排班历史记录
            getScheduleHistory() {
                try {
                    const stored = localStorage.getItem(this.historyStorageKey);
                    if (stored) {
                        return JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载历史记录失败:', error);
                }
                return [];
            }

            // 保存排班到历史记录
            saveScheduleToHistory(schedule) {
                try {
                    let history = this.getScheduleHistory();

                    // 检查是否已存在相同周的记录
                    const weekKey = schedule.week_start;
                    const existingIndex = history.findIndex(h => h.week_start === weekKey);

                    if (existingIndex >= 0) {
                        // 更新现有记录
                        history[existingIndex] = schedule;
                    } else {
                        // 添加新记录
                        history.push(schedule);
                    }

                    // 按时间排序并限制记录数量
                    history.sort((a, b) => new Date(b.week_start) - new Date(a.week_start));
                    if (history.length > this.maxHistoryWeeks) {
                        history = history.slice(0, this.maxHistoryWeeks);
                    }

                    localStorage.setItem(this.historyStorageKey, JSON.stringify(history));
                } catch (error) {
                    console.error('保存历史记录失败:', error);
                }
            }

            // 获取员工上周的休息日
            getLastWeekRestDays(history, currentWeek, employees) {
                const lastWeekRestDays = {};

                if (history.length === 0) {
                    return lastWeekRestDays;
                }

                // 计算上周的开始日期
                const lastWeek = new Date(currentWeek);
                lastWeek.setDate(lastWeek.getDate() - 7);
                const lastWeekKey = lastWeek.toISOString();

                // 查找上周的排班记录
                const lastWeekSchedule = history.find(h => h.week_start === lastWeekKey);

                if (lastWeekSchedule && lastWeekSchedule.days) {
                    employees.forEach(emp => {
                        lastWeekSchedule.days.forEach(day => {
                            if (day.assignments[emp.id] === 'rest') {
                                lastWeekRestDays[emp.id] = day.dayOfWeek;
                            }
                        });
                    });
                }

                return lastWeekRestDays;
            }

            // 支持跨周轮换的智能排班算法
            generateSmartScheduleWithRotation(weekStart, employeesByDept, history) {
                const days = [];
                const restDays = [0, 2, 3, 4, 5]; // 周日、周二、周三、周四、周五
                const allEmployees = Object.values(employeesByDept).flat();

                // 获取员工上周的休息日
                const lastWeekRestDays = this.getLastWeekRestDays(history, weekStart, allEmployees);

                // 为每个员工分配唯一的休息日（考虑轮换）
                const employeeRestAssignments = this.assignRestDaysWithRotation(
                    employeesByDept, restDays, lastWeekRestDays, history
                );

                // 创建7天的排班
                for (let i = 0; i < 7; i++) {
                    const date = new Date(weekStart);
                    date.setDate(date.getDate() + i);
                    const dayOfWeek = date.getDay();

                    const assignments = {};

                    if (WORK_DAYS.includes(dayOfWeek)) {
                        // 工作日：所有人上班
                        allEmployees.forEach(emp => {
                            assignments[emp.id] = 'work';
                        });
                    } else {
                        // 休息日：根据预分配的休息安排
                        allEmployees.forEach(emp => {
                            if (employeeRestAssignments[emp.id] === dayOfWeek) {
                                assignments[emp.id] = 'rest';
                            } else {
                                assignments[emp.id] = 'work';
                            }
                        });
                    }

                    days.push({
                        date: date.toISOString(),
                        dayOfWeek: dayOfWeek,
                        assignments: assignments
                    });
                }

                return {
                    week_start: weekStart.toISOString(),
                    days: days,
                    generated_at: new Date().toISOString()
                };
            }

            // 支持轮换的休息日分配
            assignRestDaysWithRotation(employeesByDept, restDays, lastWeekRestDays, history) {
                const assignments = {};

                // 为每个岗位分配休息日
                Object.keys(employeesByDept).forEach(dept => {
                    const employees = employeesByDept[dept];
                    if (employees.length === 0) return;

                    // 确定该岗位可用的休息日和每天的休息人数限制
                    let availableRestDays = [...restDays];
                    let maxRestPerDay = {};

                    // 设置每天的休息人数限制
                    restDays.forEach(day => {
                        if (day === 0 && dept === 'current') {
                            // 周日电流组可以2人休息
                            maxRestPerDay[day] = 2;
                        } else {
                            // 其他情况每天最多1人休息
                            maxRestPerDay[day] = 1;
                        }
                    });

                    // 为该岗位的每个员工分配休息日（考虑轮换）
                    this.distributeRestDaysWithRotation(
                        employees, availableRestDays, maxRestPerDay,
                        assignments, lastWeekRestDays, history
                    );
                });

                return assignments;
            }

            // 支持轮换的休息日分配给员工（重新设计简化版）
            distributeRestDaysWithRotation(employees, availableRestDays, maxRestPerDay, assignments, lastWeekRestDays, history) {
                // 创建休息日的使用计数
                const restDayUsage = {};
                availableRestDays.forEach(day => {
                    restDayUsage[day] = 0;
                });

                // 获取员工的历史休息日统计
                const employeeRestHistory = this.getEmployeeRestHistory(employees, history);

                // 第一步：优先分配周日休息（如果有周日）
                if (availableRestDays.includes(0) && maxRestPerDay[0] > 0) {
                    this.assignSundayRestFairly(employees, assignments, restDayUsage, maxRestPerDay[0], lastWeekRestDays, employeeRestHistory);
                }

                // 第二步：为剩余员工分配其他休息日
                const remainingEmployees = employees.filter(emp => assignments[emp.id] === undefined);

                // 简单轮换：按员工顺序分配，避免上周的休息日
                remainingEmployees.forEach(emp => {
                    const lastRestDay = lastWeekRestDays[emp.id];
                    let assigned = false;

                    // 优先选择非周日的休息日，且避免上周的休息日
                    const preferredDays = availableRestDays.filter(day =>
                        day !== 0 && day !== lastRestDay
                    );

                    // 尝试分配优选日期
                    for (let restDay of preferredDays) {
                        if (restDayUsage[restDay] < maxRestPerDay[restDay]) {
                            assignments[emp.id] = restDay;
                            restDayUsage[restDay]++;
                            assigned = true;
                            break;
                        }
                    }

                    // 如果优选日期都满了，尝试其他可用日期
                    if (!assigned) {
                        for (let restDay of availableRestDays) {
                            if (restDayUsage[restDay] < maxRestPerDay[restDay]) {
                                assignments[emp.id] = restDay;
                                restDayUsage[restDay]++;
                                assigned = true;
                                break;
                            }
                        }
                    }

                    // 兜底：如果还是没分配到，分配第一个可用日期
                    if (!assigned) {
                        assignments[emp.id] = availableRestDays[0];
                    }
                });
            }

            // 公平分配周日休息（重新设计简单有效的算法）
            assignSundayRestFairly(employees, assignments, restDayUsage, maxSundayRest, lastWeekRestDays, employeeRestHistory) {
                // 计算每个员工的周日休息次数
                const employeeSundayStats = employees.map(emp => {
                    const history = employeeRestHistory[emp.id] || {};
                    const sundayCount = history[0] || 0;
                    const lastWeekWasSunday = lastWeekRestDays[emp.id] === 0;

                    return {
                        employee: emp,
                        sundayCount: sundayCount,
                        lastWeekWasSunday: lastWeekWasSunday,
                        // 计算优先级分数（分数越低优先级越高）
                        priority: sundayCount * 10 + (lastWeekWasSunday ? 100 : 0)
                    };
                });

                // 按优先级排序（优先级分数低的在前）
                employeeSundayStats.sort((a, b) => {
                    if (a.priority !== b.priority) {
                        return a.priority - b.priority;
                    }
                    // 如果优先级相同，随机排序避免总是同样的顺序
                    return Math.random() - 0.5;
                });

                // 分配周日休息给优先级最高的员工
                for (let i = 0; i < Math.min(maxSundayRest, employeeSundayStats.length); i++) {
                    const emp = employeeSundayStats[i].employee;
                    assignments[emp.id] = 0; // 周日
                    restDayUsage[0]++;
                }

                // 调试信息（可以在控制台查看）
                if (window.console && console.log) {
                    console.log('周日分配情况:', employeeSundayStats.slice(0, maxSundayRest).map(stat => ({
                        name: stat.employee.name,
                        sundayCount: stat.sundayCount,
                        lastWeekWasSunday: stat.lastWeekWasSunday,
                        priority: stat.priority
                    })));
                }
            }

            // 获取员工的历史休息日统计
            getEmployeeRestHistory(employees, history) {
                const employeeHistory = {};

                employees.forEach(emp => {
                    employeeHistory[emp.id] = {};
                    [0, 2, 3, 4, 5].forEach(day => {
                        employeeHistory[emp.id][day] = 0;
                    });
                });

                // 统计最近几周的休息日分布
                history.slice(0, 5).forEach(schedule => { // 只看最近5周
                    if (schedule.days) {
                        employees.forEach(emp => {
                            schedule.days.forEach(day => {
                                if (day.assignments[emp.id] === 'rest' && employeeHistory[emp.id]) {
                                    employeeHistory[emp.id][day.dayOfWeek]++;
                                }
                            });
                        });
                    }
                });

                return employeeHistory;
            }

            // 按轮换优先级排序员工（重点优化周日分配）
            sortEmployeesByRotationPriority(employees, lastWeekRestDays, employeeRestHistory) {
                return employees.slice().sort((a, b) => {
                    const aLastDay = lastWeekRestDays[a.id];
                    const bLastDay = lastWeekRestDays[b.id];
                    const aHistory = employeeRestHistory[a.id] || {};
                    const bHistory = employeeRestHistory[b.id] || {};

                    // 获取周日休息次数
                    const aSundayCount = aHistory[0] || 0;
                    const bSundayCount = bHistory[0] || 0;

                    // 优先级1: 周日休息次数差异（最重要）
                    if (aSundayCount !== bSundayCount) {
                        return aSundayCount - bSundayCount; // 周日休息少的优先
                    }

                    // 优先级2: 上周是否休息了周日（避免连续周日休息）
                    const aLastSunday = aLastDay === 0 ? 1 : 0;
                    const bLastSunday = bLastDay === 0 ? 1 : 0;

                    if (aLastSunday !== bLastSunday) {
                        return aLastSunday - bLastSunday; // 上周没休周日的优先
                    }

                    // 优先级3: 上周休息日的"质量"
                    const aLastDayScore = aLastDay === 0 ? 1 : 0;
                    const bLastDayScore = bLastDay === 0 ? 1 : 0;

                    if (aLastDayScore !== bLastDayScore) {
                        return aLastDayScore - bLastDayScore; // 上周休息日"差"的优先
                    }

                    // 优先级4: 总体休息日均衡性
                    const aTotalRest = Object.values(aHistory).reduce((sum, count) => sum + count, 0);
                    const bTotalRest = Object.values(bHistory).reduce((sum, count) => sum + count, 0);

                    return aTotalRest - bTotalRest; // 总休息次数少的优先
                });
            }

            // 获取员工的候选休息日（优化周日分配）
            getCandidateRestDays(availableRestDays, lastRestDay, employeeHistory) {
                const candidates = [...availableRestDays];

                // 获取周日休息次数
                const sundayCount = employeeHistory[0] || 0;

                // 如果上周休息了周日，这周周日优先级降到最低
                if (lastRestDay === 0) {
                    const sundayIndex = candidates.indexOf(0);
                    if (sundayIndex > -1) {
                        candidates.splice(sundayIndex, 1);
                        candidates.push(0); // 放到最后
                    }
                }

                // 根据历史记录调整优先级
                candidates.sort((a, b) => {
                    const aCount = employeeHistory[a] || 0;
                    const bCount = employeeHistory[b] || 0;

                    // 特殊处理周日
                    if (a === 0 && b !== 0) {
                        // 如果A是周日，B不是周日
                        if (lastRestDay === 0) {
                            return 1; // 上周休了周日，这周周日优先级低
                        }
                        // 周日休息次数少的话，优先级高
                        return sundayCount === 0 ? -2 : (sundayCount <= 1 ? -1 : 1);
                    }

                    if (b === 0 && a !== 0) {
                        // 如果B是周日，A不是周日
                        if (lastRestDay === 0) {
                            return -1; // 上周休了周日，这周周日优先级低
                        }
                        return sundayCount === 0 ? 2 : (sundayCount <= 1 ? 1 : -1);
                    }

                    // 都不是周日，或都是周日，按休息次数排序
                    return aCount - bCount;
                });

                return candidates;
            }



            // 验证排班规则
            validateSchedule(schedule, employees) {
                const results = {
                    valid: true,
                    errors: [],
                    warnings: []
                };

                if (!schedule || !schedule.days) {
                    results.valid = false;
                    results.errors.push('排班表数据无效');
                    return results;
                }

                const activeEmployees = employees.filter(emp => emp.is_active);

                // 检查每个员工的休息安排
                activeEmployees.forEach(emp => {
                    let restDays = 0;
                    let workDays = 0;
                    const restDaysList = [];

                    schedule.days.forEach((day, index) => {
                        const assignment = day.assignments[emp.id];
                        if (assignment === 'rest') {
                            restDays++;
                            restDaysList.push(this.getDayName(day.dayOfWeek));
                        } else if (assignment === 'work') {
                            workDays++;
                        }
                    });

                    // 每个员工一周内必须有且仅有1天休息
                    if (restDays === 0) {
                        results.errors.push(`${emp.name} 一周内没有休息日`);
                        results.valid = false;
                    } else if (restDays > 1) {
                        results.errors.push(`${emp.name} 一周内休息了${restDays}天 (${restDaysList.join('、')})，超过限制`);
                        results.valid = false;
                    }
                });

                // 检查工作日是否有足够人员
                schedule.days.forEach((day, index) => {
                    const dayOfWeek = new Date(day.date).getDay();

                    if (WORK_DAYS.includes(dayOfWeek)) {
                        // 工作日检查
                        Object.keys(DEPARTMENTS).forEach(dept => {
                            const deptEmployees = activeEmployees.filter(emp => emp.department === dept);
                            const workingCount = deptEmployees.filter(emp =>
                                day.assignments[emp.id] === 'work'
                            ).length;

                            if (workingCount === 0 && deptEmployees.length > 0) {
                                results.errors.push(`${DEPARTMENTS[dept].name} 在工作日无人值班`);
                                results.valid = false;
                            }
                        });
                    } else {
                        // 休息日检查
                        Object.keys(DEPARTMENTS).forEach(dept => {
                            const deptEmployees = activeEmployees.filter(emp => emp.department === dept);
                            const restingCount = deptEmployees.filter(emp =>
                                day.assignments[emp.id] === 'rest'
                            ).length;

                            const maxRest = (dept === 'current' && dayOfWeek === 0) ? 2 : 1;

                            if (restingCount > maxRest) {
                                results.warnings.push(`${DEPARTMENTS[dept].name} 在${this.getDayName(dayOfWeek)}休息人数超过限制`);
                            }
                        });
                    }
                });

                return results;
            }

            // 获取星期名称
            getDayName(dayOfWeek) {
                const names = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
                return names[dayOfWeek];
            }

            // 保存排班表（按周存储）
            saveSchedule(schedule) {
                try {
                    // 使用周开始日期作为键值
                    const weekKey = this.getWeekKey(schedule.week_start);
                    const scheduleStorageKey = `${this.storageKey}_${weekKey}`;
                    localStorage.setItem(scheduleStorageKey, JSON.stringify(schedule));

                    // 同时更新周列表索引
                    this.updateWeekIndex(weekKey);
                } catch (error) {
                    console.error('保存排班表失败:', error);
                }
            }

            // 加载指定周的排班表
            loadSchedule(weekStart = null) {
                try {
                    let weekKey;
                    if (weekStart) {
                        weekKey = this.getWeekKey(weekStart);
                    } else {
                        // 如果没有指定周，尝试加载当前周
                        weekKey = this.getWeekKey(new Date());
                    }

                    const scheduleStorageKey = `${this.storageKey}_${weekKey}`;
                    const stored = localStorage.getItem(scheduleStorageKey);
                    if (stored) {
                        return JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载排班表失败:', error);
                }
                return null;
            }

            // 获取周键值（格式：YYYY-MM-DD）
            getWeekKey(dateInput) {
                const date = new Date(dateInput);
                // 确保是周一
                const monday = new Date(date);
                monday.setDate(monday.getDate() - monday.getDay() + 1);
                return monday.toISOString().split('T')[0];
            }

            // 更新周索引
            updateWeekIndex(weekKey) {
                try {
                    const indexKey = `${this.storageKey}_index`;
                    let weekIndex = [];

                    const stored = localStorage.getItem(indexKey);
                    if (stored) {
                        weekIndex = JSON.parse(stored);
                    }

                    // 添加新周到索引（如果不存在）
                    if (!weekIndex.includes(weekKey)) {
                        weekIndex.push(weekKey);
                        weekIndex.sort(); // 按日期排序
                        localStorage.setItem(indexKey, JSON.stringify(weekIndex));
                    }
                } catch (error) {
                    console.error('更新周索引失败:', error);
                }
            }

            // 获取所有已保存的周
            getAllSavedWeeks() {
                try {
                    const indexKey = `${this.storageKey}_index`;
                    const stored = localStorage.getItem(indexKey);
                    if (stored) {
                        return JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('获取周索引失败:', error);
                }
                return [];
            }

            // 删除指定周的排班数据
            deleteSchedule(weekStart) {
                try {
                    const weekKey = this.getWeekKey(weekStart);
                    const scheduleStorageKey = `${this.storageKey}_${weekKey}`;
                    localStorage.removeItem(scheduleStorageKey);

                    // 从索引中移除
                    const indexKey = `${this.storageKey}_index`;
                    let weekIndex = this.getAllSavedWeeks();
                    weekIndex = weekIndex.filter(key => key !== weekKey);
                    localStorage.setItem(indexKey, JSON.stringify(weekIndex));
                } catch (error) {
                    console.error('删除排班表失败:', error);
                }
            }
        }

        // 创建管理器实例
        const employeeManager = new EmployeeManager();
        const scheduleAlgorithm = new ScheduleAlgorithm();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            employees = employeeManager.loadEmployees();
            // 加载当前周的排班数据
            currentSchedule = scheduleAlgorithm.loadSchedule(currentWeek);

            renderDepartments();
            updateWeekDisplay();
            renderScheduleTable();
            refreshStats();

            // 如果有排班数据，显示相关按钮
            if (currentSchedule) {
                const rotationBtn = document.getElementById('rotationInfoBtn');
                if (rotationBtn) {
                    rotationBtn.style.display = 'inline-flex';
                }

                const exportBtn = document.getElementById('exportBtn');
                if (exportBtn) {
                    exportBtn.style.display = 'inline-flex';
                }
            }
        });

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的活跃状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');

            // 激活选中的标签
            event.target.classList.add('active');

            // 如果切换到数据管理页面，更新动态内容
            if (tabName === 'data-management') {
                updateDataManagementPage();
            }
        }

        // 更新数据管理页面的动态内容
        function updateDataManagementPage() {
            // 更新员工数据标签
            const employeeLabel = document.getElementById('employeeDataLabel');
            if (employeeLabel) {
                employeeLabel.textContent = `员工数据 (${employees.length}人)`;
            }

            // 更新排班数据标签
            const scheduleLabel = document.getElementById('scheduleDataLabel');
            if (scheduleLabel) {
                scheduleLabel.textContent = `排班表数据 ${currentSchedule ? '(已生成)' : '(未生成)'}`;
            }

            // 如果没有排班数据，禁用排班数据导出选项
            const scheduleCheckbox = document.getElementById('exportSchedule');
            if (scheduleCheckbox) {
                scheduleCheckbox.disabled = !currentSchedule;
                if (!currentSchedule) {
                    scheduleCheckbox.checked = false;
                }
            }
        }

        // 渲染岗位管理页面
        function renderDepartments() {
            const container = document.getElementById('departmentGrid');
            container.innerHTML = '';

            Object.keys(DEPARTMENTS).forEach(deptKey => {
                const dept = DEPARTMENTS[deptKey];
                const deptEmployees = employees.filter(emp => emp.department === deptKey);

                const card = document.createElement('div');
                card.className = 'department-card';
                card.innerHTML = `
                    <div class="department-header">
                        <div class="department-title">
                            <i class="${dept.icon}" style="color: ${dept.color}"></i>
                            ${dept.name}
                        </div>
                        <span class="badge">${deptEmployees.length}/${dept.maxEmployees}人</span>
                    </div>
                    <div class="department-body">
                        <ul class="employee-list" id="dept-${deptKey}">
                            ${deptEmployees.map(emp => `
                                <li class="employee-item">
                                    <div class="employee-name">${emp.name}</div>
                                    <div class="employee-status">
                                        <span class="status-badge ${emp.is_active ? 'status-active' : 'status-inactive'}">
                                            ${emp.is_active ? '启用' : '停用'}
                                        </span>
                                        <button class="btn btn-sm btn-warning" onclick="editEmployee('${emp.id}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm ${emp.is_active ? 'btn-secondary' : 'btn-success'}"
                                                onclick="toggleEmployeeStatus('${emp.id}')">
                                            <i class="fas ${emp.is_active ? 'fa-pause' : 'fa-play'}"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteEmployee('${emp.id}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // 显示添加员工模态框
        function showAddEmployeeModal() {
            document.getElementById('addEmployeeModal').style.display = 'flex';
            document.getElementById('employeeName').focus();
        }

        // 隐藏添加员工模态框
        function hideAddEmployeeModal() {
            document.getElementById('addEmployeeModal').style.display = 'none';
            document.getElementById('addEmployeeForm').reset();
        }

        // 添加员工
        function addEmployee() {
            const name = document.getElementById('employeeName').value.trim();
            const department = document.getElementById('employeeDepartment').value;
            const isActive = document.getElementById('employeeStatus').value === 'true';

            if (!name) {
                showMessage('请输入员工姓名', 'error');
                return;
            }

            if (!department) {
                showMessage('请选择岗位', 'error');
                return;
            }

            try {
                employeeManager.addEmployee(name, department, isActive);
                hideAddEmployeeModal();
                renderDepartments();
                showMessage('员工添加成功', 'success');
            } catch (error) {
                showMessage(error.message, 'error');
            }
        }

        // 编辑员工
        function editEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (!employee) {
                showMessage('员工不存在', 'error');
                return;
            }

            document.getElementById('editEmployeeId').value = employee.id;
            document.getElementById('editEmployeeName').value = employee.name;
            document.getElementById('editEmployeeDepartment').value = employee.department;
            document.getElementById('editEmployeeStatus').value = employee.is_active.toString();

            document.getElementById('editEmployeeModal').style.display = 'flex';
            document.getElementById('editEmployeeName').focus();
        }

        // 隐藏编辑员工模态框
        function hideEditEmployeeModal() {
            document.getElementById('editEmployeeModal').style.display = 'none';
            document.getElementById('editEmployeeForm').reset();
        }

        // 更新员工
        function updateEmployee() {
            const id = document.getElementById('editEmployeeId').value;
            const name = document.getElementById('editEmployeeName').value.trim();
            const department = document.getElementById('editEmployeeDepartment').value;
            const isActive = document.getElementById('editEmployeeStatus').value === 'true';

            if (!name) {
                showMessage('请输入员工姓名', 'error');
                return;
            }

            try {
                employeeManager.updateEmployee(id, name, department, isActive);
                hideEditEmployeeModal();
                renderDepartments();
                renderScheduleTable(); // 重新渲染排班表
                showMessage('员工信息更新成功', 'success');
            } catch (error) {
                showMessage(error.message, 'error');
            }
        }

        // 切换员工状态
        function toggleEmployeeStatus(id) {
            try {
                employeeManager.toggleEmployeeStatus(id);
                renderDepartments();
                renderScheduleTable(); // 重新渲染排班表
                showMessage('员工状态更新成功', 'success');
            } catch (error) {
                showMessage(error.message, 'error');
            }
        }

        // 删除员工
        function deleteEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (!employee) {
                showMessage('员工不存在', 'error');
                return;
            }

            if (confirm(`确定要删除员工 ${employee.name} 吗？`)) {
                try {
                    employeeManager.deleteEmployee(id);
                    renderDepartments();
                    renderScheduleTable(); // 重新渲染排班表
                    showMessage('员工删除成功', 'success');
                } catch (error) {
                    showMessage(error.message, 'error');
                }
            }
        }

        // 更新周显示
        function updateWeekDisplay() {
            const monday = new Date(currentWeek);
            monday.setDate(monday.getDate() - monday.getDay() + 1);

            const sunday = new Date(monday);
            sunday.setDate(sunday.getDate() + 6);

            const formatDate = (date) => {
                return `${date.getMonth() + 1}/${date.getDate()}`;
            };

            document.getElementById('currentWeekDisplay').textContent =
                `${formatDate(monday)} - ${formatDate(sunday)}`;

            // 显示周状态
            const weekKey = scheduleAlgorithm.getWeekKey(monday);
            const hasSchedule = scheduleAlgorithm.loadSchedule(monday) !== null;
            const statusEl = document.getElementById('weekStatus');

            if (hasSchedule) {
                statusEl.innerHTML = `<i class="fas fa-check-circle" style="color: #27ae60;"></i> 已生成排班`;
                statusEl.style.color = '#27ae60';
            } else {
                statusEl.innerHTML = `<i class="fas fa-clock" style="color: #f39c12;"></i> 未生成排班`;
                statusEl.style.color = '#f39c12';
            }
        }

        // 切换周
        function changeWeek(direction) {
            const newWeek = new Date(currentWeek);
            newWeek.setDate(newWeek.getDate() + (direction * 7));
            currentWeek = newWeek;

            // 加载新周的排班数据
            currentSchedule = scheduleAlgorithm.loadSchedule(currentWeek);

            updateWeekDisplay();
            renderScheduleTable();
            refreshStats();

            // 更新轮换信息按钮和导出按钮显示状态
            const rotationBtn = document.getElementById('rotationInfoBtn');
            if (rotationBtn) {
                rotationBtn.style.display = currentSchedule ? 'inline-flex' : 'none';
            }

            const exportBtn = document.getElementById('exportBtn');
            if (exportBtn) {
                exportBtn.style.display = currentSchedule ? 'inline-flex' : 'none';
            }
        }

        // 生成排班
        function generateSchedule() {
            try {
                const activeEmployees = employees.filter(emp => emp.is_active);
                if (activeEmployees.length === 0) {
                    showMessage('没有活跃的员工，无法生成排班', 'error');
                    return;
                }

                // 生成当前周的排班
                currentSchedule = scheduleAlgorithm.generateSchedule(employees, currentWeek);

                // 保存到对应周的存储位置
                scheduleAlgorithm.saveSchedule(currentSchedule);

                renderScheduleTable();
                refreshStats();

                // 显示轮换信息按钮和导出按钮
                const rotationBtn = document.getElementById('rotationInfoBtn');
                if (rotationBtn) {
                    rotationBtn.style.display = 'inline-flex';
                }

                const exportBtn = document.getElementById('exportBtn');
                if (exportBtn) {
                    exportBtn.style.display = 'inline-flex';
                }

                // 显示周信息
                const weekKey = scheduleAlgorithm.getWeekKey(currentWeek);
                showMessage(`排班表生成成功（${weekKey}周，支持跨周轮换）`, 'success');
            } catch (error) {
                showMessage('生成排班失败: ' + error.message, 'error');
            }
        }

        // 显示轮换信息
        function showRotationInfo() {
            if (!currentSchedule) {
                showMessage('请先生成排班表', 'warning');
                return;
            }

            const history = scheduleAlgorithm.getScheduleHistory();
            const activeEmployees = employees.filter(emp => emp.is_active);

            // 获取上周的休息安排
            const lastWeekRestDays = scheduleAlgorithm.getLastWeekRestDays(history, new Date(currentSchedule.week_start), activeEmployees);

            // 获取本周的休息安排
            const thisWeekRestDays = {};
            currentSchedule.days.forEach(day => {
                activeEmployees.forEach(emp => {
                    if (day.assignments[emp.id] === 'rest') {
                        thisWeekRestDays[emp.id] = day.dayOfWeek;
                    }
                });
            });

            // 生成轮换信息HTML
            let infoHTML = `
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; margin-top: 1rem;">
                    <h4 style="color: #2c3e50; margin-bottom: 1rem;">
                        <i class="fas fa-sync-alt"></i>
                        跨周轮换信息
                    </h4>
            `;

            if (Object.keys(lastWeekRestDays).length === 0) {
                infoHTML += `
                    <p style="color: #666; margin-bottom: 1rem;">
                        <i class="fas fa-info-circle"></i>
                        这是第一周排班，没有上周数据进行对比。
                    </p>
                `;
            } else {
                infoHTML += `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                `;

                // 按岗位分组显示
                Object.keys(DEPARTMENTS).forEach(deptKey => {
                    const dept = DEPARTMENTS[deptKey];
                    const deptEmployees = activeEmployees.filter(emp => emp.department === deptKey);

                    if (deptEmployees.length > 0) {
                        infoHTML += `
                            <div style="background: white; padding: 1rem; border-radius: 4px; border-left: 4px solid ${dept.color};">
                                <h5 style="color: ${dept.color}; margin-bottom: 0.5rem;">
                                    <i class="${dept.icon}"></i>
                                    ${dept.name}
                                </h5>
                                <table style="width: 100%; font-size: 0.9rem;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 0.25rem; text-align: left;">员工</th>
                                            <th style="padding: 0.25rem; text-align: center;">上周</th>
                                            <th style="padding: 0.25rem; text-align: center;">本周</th>
                                            <th style="padding: 0.25rem; text-align: center;">变化</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                        deptEmployees.forEach(emp => {
                            const lastDay = lastWeekRestDays[emp.id];
                            const thisDay = thisWeekRestDays[emp.id];
                            const lastDayName = lastDay !== undefined ? getDayName(lastDay) : '无数据';
                            const thisDayName = thisDay !== undefined ? getDayName(thisDay) : '无休息';

                            let changeIcon = '';
                            let changeColor = '#666';

                            if (lastDay !== undefined && thisDay !== undefined) {
                                if (lastDay !== thisDay) {
                                    changeIcon = '<i class="fas fa-sync-alt" style="color: #27ae60;"></i>';
                                    changeColor = '#27ae60';
                                } else {
                                    changeIcon = '<i class="fas fa-equals" style="color: #f39c12;"></i>';
                                    changeColor = '#f39c12';
                                }
                            }

                            infoHTML += `
                                <tr>
                                    <td style="padding: 0.25rem;">${emp.name}</td>
                                    <td style="padding: 0.25rem; text-align: center;">${lastDayName}</td>
                                    <td style="padding: 0.25rem; text-align: center;">${thisDayName}</td>
                                    <td style="padding: 0.25rem; text-align: center; color: ${changeColor};">${changeIcon}</td>
                                </tr>
                            `;
                        });

                        infoHTML += `
                                    </tbody>
                                </table>
                            </div>
                        `;
                    }
                });

                infoHTML += `</div>`;
            }

            // 显示轮换统计
            const rotationStats = calculateRotationStats(history, activeEmployees);
            if (rotationStats.totalWeeks > 1) {
                infoHTML += `
                    <div style="margin-top: 1rem; padding: 1rem; background: white; border-radius: 4px;">
                        <h5 style="color: #2c3e50; margin-bottom: 0.5rem;">
                            <i class="fas fa-chart-pie"></i>
                            轮换统计 (最近${rotationStats.totalWeeks}周)
                        </h5>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; font-size: 0.9rem;">
                            <div>
                                <strong>轮换率:</strong> ${rotationStats.rotationRate.toFixed(1)}%
                            </div>
                            <div>
                                <strong>周日休息分布:</strong> ${rotationStats.sundayDistribution}
                            </div>
                            <div>
                                <strong>最均衡员工:</strong> ${rotationStats.mostBalanced}
                            </div>
                        </div>
                    </div>
                `;
            }

            // 显示周日休息详细统计
            const sundayStats = calculateSundayStats(history, activeEmployees);
            if (sundayStats.totalWeeks > 0) {
                infoHTML += `
                    <div style="margin-top: 1rem; padding: 1rem; background: #fff3cd; border-radius: 4px; border-left: 4px solid #f39c12;">
                        <h5 style="color: #856404; margin-bottom: 0.5rem;">
                            <i class="fas fa-sun"></i>
                            周日休息公平性分析 (最近${sundayStats.totalWeeks}周)
                        </h5>
                        <div style="font-size: 0.9rem; color: #856404;">
                            <div style="margin-bottom: 0.5rem;">
                                <strong>周日休息分配：</strong>
                            </div>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 0.5rem;">
                                ${sundayStats.employeeStats.map(stat => `
                                    <div style="display: flex; justify-content: space-between; padding: 0.25rem 0.5rem; background: rgba(255,255,255,0.5); border-radius: 3px;">
                                        <span>${stat.name}</span>
                                        <span><strong>${stat.sundayCount}次</strong> (${stat.percentage.toFixed(1)}%)</span>
                                    </div>
                                `).join('')}
                            </div>
                            <div style="margin-top: 0.5rem; font-size: 0.8rem;">
                                <strong>公平性指标：</strong>
                                ${sundayStats.fairnessScore >= 0.8 ?
                                    '<span style="color: #27ae60;">✓ 分配公平</span>' :
                                    '<span style="color: #e74c3c;">⚠ 需要调整</span>'
                                }
                                (${(sundayStats.fairnessScore * 100).toFixed(1)}分)
                            </div>
                        </div>
                    </div>
                `;
            }

            infoHTML += `
                    <div style="margin-top: 1rem; padding: 0.75rem; background: #e3f2fd; border-radius: 4px; font-size: 0.9rem;">
                        <i class="fas fa-lightbulb" style="color: #1976d2;"></i>
                        <strong>轮换说明：</strong>
                        系统会自动避免员工连续多周在同一天休息，优先安排上周休息日"不理想"的员工获得更好的休息日。
                    </div>
                </div>
            `;

            // 在排班表下方显示轮换信息
            const container = document.getElementById('scheduleTableContainer');
            const existingInfo = container.querySelector('.rotation-info');
            if (existingInfo) {
                existingInfo.remove();
            }

            const infoDiv = document.createElement('div');
            infoDiv.className = 'rotation-info';
            infoDiv.innerHTML = infoHTML;
            container.appendChild(infoDiv);
        }

        // 获取星期名称
        function getDayName(dayOfWeek) {
            const names = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            return names[dayOfWeek];
        }

        // 计算轮换统计
        function calculateRotationStats(history, employees) {
            const stats = {
                totalWeeks: history.length,
                rotationRate: 0,
                sundayDistribution: '暂无数据',
                mostBalanced: '暂无数据'
            };

            if (history.length < 2) {
                return stats;
            }

            // 计算轮换率
            let totalRotations = 0;
            let totalComparisons = 0;

            for (let i = 0; i < history.length - 1; i++) {
                const thisWeek = history[i];
                const nextWeek = history[i + 1];

                employees.forEach(emp => {
                    const thisRestDay = getEmployeeRestDay(thisWeek, emp.id);
                    const nextRestDay = getEmployeeRestDay(nextWeek, emp.id);

                    if (thisRestDay !== undefined && nextRestDay !== undefined) {
                        totalComparisons++;
                        if (thisRestDay !== nextRestDay) {
                            totalRotations++;
                        }
                    }
                });
            }

            if (totalComparisons > 0) {
                stats.rotationRate = (totalRotations / totalComparisons) * 100;
            }

            return stats;
        }

        // 获取员工在某周的休息日
        function getEmployeeRestDay(schedule, employeeId) {
            if (!schedule || !schedule.days) return undefined;

            for (let day of schedule.days) {
                if (day.assignments[employeeId] === 'rest') {
                    return day.dayOfWeek;
                }
            }
            return undefined;
        }

        // 计算周日休息统计
        function calculateSundayStats(history, employees) {
            const stats = {
                totalWeeks: history.length,
                employeeStats: [],
                fairnessScore: 0
            };

            if (history.length === 0) {
                return stats;
            }

            // 统计每个员工的周日休息次数
            const sundayCounts = {};
            employees.forEach(emp => {
                sundayCounts[emp.id] = 0;
            });

            // 遍历历史记录
            history.forEach(schedule => {
                if (schedule.days) {
                    employees.forEach(emp => {
                        const restDay = getEmployeeRestDay(schedule, emp.id);
                        if (restDay === 0) { // 周日
                            sundayCounts[emp.id]++;
                        }
                    });
                }
            });

            // 计算统计信息
            const totalSundayRests = Object.values(sundayCounts).reduce((sum, count) => sum + count, 0);

            stats.employeeStats = employees.map(emp => ({
                name: emp.name,
                sundayCount: sundayCounts[emp.id],
                percentage: totalSundayRests > 0 ? (sundayCounts[emp.id] / totalSundayRests) * 100 : 0
            })).sort((a, b) => b.sundayCount - a.sundayCount);

            // 计算公平性得分（重新设计更直观的算法）
            if (employees.length > 0 && history.length > 0) {
                const counts = Object.values(sundayCounts);
                const maxCount = Math.max(...counts);
                const minCount = Math.min(...counts);
                const totalWeeks = history.length;

                // 理想情况：每个员工应该休息的周日次数
                const idealCount = totalWeeks / employees.length;

                // 计算偏差程度
                let totalDeviation = 0;
                counts.forEach(count => {
                    totalDeviation += Math.abs(count - idealCount);
                });

                const averageDeviation = totalDeviation / employees.length;
                const maxPossibleDeviation = Math.max(idealCount, totalWeeks - idealCount);

                // 公平性得分：偏差越小得分越高（0-1之间）
                stats.fairnessScore = Math.max(0, 1 - (averageDeviation / maxPossibleDeviation));

                // 调试信息
                if (window.console && console.log) {
                    console.log('周日公平性计算:', {
                        totalWeeks,
                        employeeCount: employees.length,
                        idealCount: idealCount.toFixed(2),
                        counts,
                        maxCount,
                        minCount,
                        averageDeviation: averageDeviation.toFixed(2),
                        fairnessScore: stats.fairnessScore.toFixed(3)
                    });
                }
            }

            return stats;
        }

        // 重置周日分配历史
        function resetSundayHistory() {
            if (!confirm('确定要重置周日分配历史吗？\n\n这将清除所有历史排班记录，重新开始公平的周日分配。\n此操作不可撤销！')) {
                return;
            }

            try {
                // 清除历史记录
                localStorage.removeItem(scheduleAlgorithm.historyStorageKey);

                // 清除所有周的排班数据
                const savedWeeks = scheduleAlgorithm.getAllSavedWeeks();
                savedWeeks.forEach(weekKey => {
                    const scheduleStorageKey = `${scheduleAlgorithm.storageKey}_${weekKey}`;
                    localStorage.removeItem(scheduleStorageKey);
                });

                // 清除周索引
                const indexKey = `${scheduleAlgorithm.storageKey}_index`;
                localStorage.removeItem(indexKey);

                // 重置当前排班
                currentSchedule = null;

                // 刷新界面
                renderScheduleTable();
                refreshStats();

                // 隐藏相关按钮
                const rotationBtn = document.getElementById('rotationInfoBtn');
                if (rotationBtn) {
                    rotationBtn.style.display = 'none';
                }

                const exportBtn = document.getElementById('exportBtn');
                if (exportBtn) {
                    exportBtn.style.display = 'none';
                }

                showMessage('周日分配历史已重置，现在可以重新开始公平分配', 'success');
            } catch (error) {
                console.error('重置失败:', error);
                showMessage('重置失败: ' + error.message, 'error');
            }
        }

        // 显示周导航
        function showWeekNavigation() {
            const savedWeeks = scheduleAlgorithm.getAllSavedWeeks();
            const currentWeekKey = scheduleAlgorithm.getWeekKey(currentWeek);

            let navHTML = `
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; margin-top: 1rem;">
                    <h4 style="color: #2c3e50; margin-bottom: 1rem;">
                        <i class="fas fa-calendar-alt"></i>
                        周次导航
                    </h4>
            `;

            if (savedWeeks.length === 0) {
                navHTML += `
                    <p style="color: #666; text-align: center; padding: 1rem;">
                        <i class="fas fa-info-circle"></i>
                        暂无已保存的排班数据
                    </p>
                `;
            } else {
                navHTML += `
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 0.5rem;">
                `;

                // 按时间倒序显示（最新的在前）
                const sortedWeeks = [...savedWeeks].sort((a, b) => new Date(b) - new Date(a));

                sortedWeeks.forEach(weekKey => {
                    const weekDate = new Date(weekKey);
                    const sunday = new Date(weekDate);
                    sunday.setDate(sunday.getDate() + 6);

                    const formatDate = (date) => {
                        return `${date.getMonth() + 1}/${date.getDate()}`;
                    };

                    const isCurrentWeek = weekKey === currentWeekKey;
                    const buttonClass = isCurrentWeek ? 'btn-primary' : 'btn-secondary';
                    const buttonStyle = isCurrentWeek ? 'font-weight: bold;' : '';

                    navHTML += `
                        <button class="btn ${buttonClass} btn-sm"
                                onclick="navigateToWeek('${weekKey}')"
                                style="${buttonStyle} width: 100%; text-align: left;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span>${formatDate(weekDate)} - ${formatDate(sunday)}</span>
                                ${isCurrentWeek ? '<i class="fas fa-arrow-left"></i>' : ''}
                            </div>
                            <small style="opacity: 0.8;">${weekKey}</small>
                        </button>
                    `;
                });

                navHTML += `</div>`;
            }

            navHTML += `
                    <div style="margin-top: 1rem; padding: 0.75rem; background: #e3f2fd; border-radius: 4px; font-size: 0.9rem;">
                        <i class="fas fa-lightbulb" style="color: #1976d2;"></i>
                        <strong>使用说明：</strong>
                        点击任意周次可以快速跳转查看该周的排班表。当前周次会以蓝色高亮显示。
                    </div>
                </div>
            `;

            // 在排班表下方显示周导航
            const container = document.getElementById('scheduleTableContainer');
            const existingNav = container.querySelector('.week-navigation');
            if (existingNav) {
                existingNav.remove();
            }

            const navDiv = document.createElement('div');
            navDiv.className = 'week-navigation';
            navDiv.innerHTML = navHTML;
            container.appendChild(navDiv);
        }

        // 导航到指定周
        function navigateToWeek(weekKey) {
            const weekDate = new Date(weekKey);
            currentWeek = weekDate;

            // 加载该周的排班数据
            currentSchedule = scheduleAlgorithm.loadSchedule(currentWeek);

            updateWeekDisplay();
            renderScheduleTable();
            refreshStats();

            // 更新按钮显示状态
            const rotationBtn = document.getElementById('rotationInfoBtn');
            if (rotationBtn) {
                rotationBtn.style.display = currentSchedule ? 'inline-flex' : 'none';
            }

            const exportBtn = document.getElementById('exportBtn');
            if (exportBtn) {
                exportBtn.style.display = currentSchedule ? 'inline-flex' : 'none';
            }

            // 移除导航面板
            const existingNav = document.querySelector('.week-navigation');
            if (existingNav) {
                existingNav.remove();
            }

            showMessage(`已切换到 ${weekKey} 周`, 'success');
        }

        // 切换导出菜单
        function toggleExportMenu() {
            const menu = document.getElementById('exportMenu');
            const isVisible = menu.style.display === 'block';
            menu.style.display = isVisible ? 'none' : 'block';
        }

        // 点击其他地方关闭导出菜单
        document.addEventListener('click', function(e) {
            const exportBtn = document.getElementById('exportBtn');
            const exportMenu = document.getElementById('exportMenu');

            if (exportBtn && exportMenu && !exportBtn.contains(e.target) && !exportMenu.contains(e.target)) {
                exportMenu.style.display = 'none';
            }
        });

        // 直接导出当前显示的排班表
        function createExportFromCurrentTable() {
            if (!currentSchedule) {
                throw new Error('没有排班数据可导出');
            }

            // 获取当前显示的排班表
            const currentTable = document.querySelector('.schedule-table');
            if (!currentTable) {
                throw new Error('找不到排班表');
            }

            // 获取周信息
            const weekStart = new Date(currentSchedule.week_start);
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);

            const formatDate = (date) => {
                return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
            };

            // 克隆当前表格
            const clonedTable = currentTable.cloneNode(true);

            // 移除所有的select元素，替换为纯文本
            const selects = clonedTable.querySelectorAll('select');
            selects.forEach(select => {
                const selectedText = select.options[select.selectedIndex].text;
                const span = document.createElement('span');
                span.textContent = selectedText;
                span.className = select.className.replace('shift-select', '');
                select.parentNode.replaceChild(span, select);
            });

            // 创建完整的导出HTML
            const html = `
                <div style="padding: 20px; background: white; font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; margin-bottom: 10px; font-size: 1.5rem; font-weight: 600;">
                            <i class="fas fa-calendar-alt" style="margin-right: 0.5rem; color: #3498db;"></i>
                            三岗位排班表
                        </h2>
                        <h3 style="color: #666; font-weight: normal; margin-bottom: 0.5rem;">
                            ${formatDate(weekStart)} - ${formatDate(weekEnd)}
                        </h3>
                        <p style="color: #999; font-size: 14px;">
                            生成时间：${new Date(currentSchedule.generated_at).toLocaleString('zh-CN')}
                        </p>
                    </div>

                    ${clonedTable.outerHTML}

                    <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                            <div><strong>排班规则：</strong></div>
                            <div>• 工作日：周一、周六</div>
                            <div>• 休息日：周二、周三、周四、周五、周日</div>
                            <div>• 每人每周休息1天</div>
                            <div>• 电流组周日最多2人休息</div>
                            <div>• 其他组休息日最多1人休息</div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        // 导出为图片
        async function exportAsImage() {
            try {
                document.getElementById('exportMenu').style.display = 'none';
                showMessage('正在生成图片，请稍候...', 'info');

                const exportHtml = createExportFromCurrentTable();

                // 创建临时容器
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = exportHtml;
                tempDiv.style.position = 'absolute';
                tempDiv.style.left = '-9999px';
                tempDiv.style.top = '0';
                tempDiv.style.width = '1200px'; // 固定宽度确保布局
                document.body.appendChild(tempDiv);

                // 等待一下确保样式加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 使用html2canvas生成图片
                const canvas = await html2canvas(tempDiv, {
                    backgroundColor: '#ffffff',
                    scale: 2, // 提高清晰度
                    useCORS: true,
                    allowTaint: true,
                    width: 1200,
                    height: tempDiv.scrollHeight
                });

                // 移除临时容器
                document.body.removeChild(tempDiv);

                // 下载图片
                const link = document.createElement('a');
                link.download = `排班表_${scheduleAlgorithm.getWeekKey(currentWeek)}.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();

                showMessage('图片导出成功', 'success');
            } catch (error) {
                console.error('导出图片失败:', error);
                showMessage('导出图片失败: ' + error.message, 'error');
            }
        }

        // 导出为PDF
        async function exportAsPDF() {
            try {
                document.getElementById('exportMenu').style.display = 'none';
                showMessage('正在生成PDF，请稍候...', 'info');

                const exportHtml = createExportFromCurrentTable();

                // 创建临时容器
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = exportHtml;
                tempDiv.style.position = 'absolute';
                tempDiv.style.left = '-9999px';
                tempDiv.style.top = '0';
                tempDiv.style.width = '1200px'; // 固定宽度确保布局
                document.body.appendChild(tempDiv);

                // 等待一下确保样式加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 使用html2canvas生成图片
                const canvas = await html2canvas(tempDiv, {
                    backgroundColor: '#ffffff',
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    width: 1200,
                    height: tempDiv.scrollHeight
                });

                // 移除临时容器
                document.body.removeChild(tempDiv);

                // 创建PDF
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'landscape', // 横向
                    unit: 'mm',
                    format: 'a4'
                });

                const imgData = canvas.toDataURL('image/png');
                const imgWidth = 297; // A4横向宽度
                const imgHeight = (canvas.height * imgWidth) / canvas.width;

                // 如果图片高度超过页面，进行缩放
                if (imgHeight > 210) { // A4横向高度
                    const scale = 210 / imgHeight;
                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth * scale, 210);
                } else {
                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
                }

                // 下载PDF
                pdf.save(`排班表_${scheduleAlgorithm.getWeekKey(currentWeek)}.pdf`);

                showMessage('PDF导出成功', 'success');
            } catch (error) {
                console.error('导出PDF失败:', error);
                showMessage('导出PDF失败: ' + error.message, 'error');
            }
        }

        // 渲染排班表
        function renderScheduleTable() {
            const container = document.getElementById('scheduleTableContainer');

            if (!currentSchedule) {
                const currentMonday = new Date(currentWeek);
                currentMonday.setDate(currentMonday.getDate() - currentMonday.getDay() + 1);
                const weekKey = scheduleAlgorithm.getWeekKey(currentMonday);

                container.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: #666;">
                        <i class="fas fa-calendar-times" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p>当前周（${weekKey}）暂无排班数据</p>
                        <p>请点击"生成排班"按钮创建排班表</p>
                    </div>
                `;
                return;
            }

            const activeEmployees = employees.filter(emp => emp.is_active);
            const employeesByDept = {
                current: activeEmployees.filter(emp => emp.department === 'current'),
                voltage: activeEmployees.filter(emp => emp.department === 'voltage'),
                data: activeEmployees.filter(emp => emp.department === 'data')
            };

            // 创建表格
            let tableHTML = `
                <table class="schedule-table">
                    <thead>
                        <tr>
                            <th style="width: 120px;">岗位/姓名</th>
                            ${currentSchedule.days.map(day => {
                                const date = new Date(day.date);
                                const dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()];
                                const isWorkDay = WORK_DAYS.includes(date.getDay());
                                return `<th class="${isWorkDay ? 'workday' : 'weekend'}">${dayName}<br>${date.getMonth() + 1}/${date.getDate()}</th>`;
                            }).join('')}
                        </tr>
                    </thead>
                    <tbody>
            `;

            // 按岗位分组显示
            Object.keys(DEPARTMENTS).forEach(deptKey => {
                const dept = DEPARTMENTS[deptKey];
                const deptEmployees = employeesByDept[deptKey];

                if (deptEmployees.length > 0) {
                    // 岗位标题行
                    tableHTML += `
                        <tr>
                            <td class="department-header" colspan="${currentSchedule.days.length + 1}">
                                <i class="${dept.icon}" style="color: ${dept.color}"></i>
                                ${dept.name} (${deptEmployees.length}人)
                            </td>
                        </tr>
                    `;

                    // 员工行
                    deptEmployees.forEach(emp => {
                        tableHTML += `
                            <tr>
                                <td class="employee-name-cell">${emp.name}</td>
                                ${currentSchedule.days.map((day, dayIndex) => {
                                    const assignment = day.assignments[emp.id] || 'work';
                                    const date = new Date(day.date);
                                    const isWorkDay = WORK_DAYS.includes(date.getDay());

                                    return `
                                        <td class="${isWorkDay ? 'workday' : 'weekend'}">
                                            <select class="shift-select ${assignment === 'work' ? 'shift-work' : 'shift-rest'}"
                                                    onchange="updateAssignment('${emp.id}', ${dayIndex}, this.value)">
                                                <option value="work" ${assignment === 'work' ? 'selected' : ''}>上班</option>
                                                <option value="rest" ${assignment === 'rest' ? 'selected' : ''}>休息</option>
                                            </select>
                                        </td>
                                    `;
                                }).join('')}
                            </tr>
                        `;
                    });
                }
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;

            // 显示验证结果
            const validation = scheduleAlgorithm.validateSchedule(currentSchedule, employees);
            if (!validation.valid || validation.warnings.length > 0) {
                let alertHTML = '<div style="margin-top: 1rem;">';

                if (!validation.valid) {
                    alertHTML += `
                        <div style="background: #f8d7da; color: #721c24; padding: 0.75rem; border-radius: 4px; margin-bottom: 0.5rem;">
                            <strong>排班规则违规：</strong>
                            <ul style="margin: 0.5rem 0 0 1rem;">
                                ${validation.errors.map(error => `<li>${error}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }

                if (validation.warnings.length > 0) {
                    alertHTML += `
                        <div style="background: #fff3cd; color: #856404; padding: 0.75rem; border-radius: 4px;">
                            <strong>注意事项：</strong>
                            <ul style="margin: 0.5rem 0 0 1rem;">
                                ${validation.warnings.map(warning => `<li>${warning}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }

                alertHTML += '</div>';
                container.innerHTML += alertHTML;
            }
        }

        // 更新排班分配
        function updateAssignment(employeeId, dayIndex, assignment) {
            if (!currentSchedule || !currentSchedule.days[dayIndex]) {
                showMessage('排班数据异常', 'error');
                return;
            }

            currentSchedule.days[dayIndex].assignments[employeeId] = assignment;

            // 保存到对应周的存储位置
            scheduleAlgorithm.saveSchedule(currentSchedule);

            // 同时更新历史记录
            scheduleAlgorithm.saveScheduleToHistory(currentSchedule);

            // 更新选择框样式
            const selectElement = event.target;
            selectElement.className = `shift-select ${assignment === 'work' ? 'shift-work' : 'shift-rest'}`;

            refreshStats();
            showMessage('排班更新成功', 'success');
        }

        // 刷新统计数据
        function refreshStats() {
            const statsGrid = document.getElementById('statsGrid');
            const detailedStats = document.getElementById('detailedStats');

            if (!currentSchedule) {
                statsGrid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: #666;">
                        <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p>暂无排班数据，无法生成统计</p>
                    </div>
                `;
                detailedStats.innerHTML = '';
                return;
            }

            const activeEmployees = employees.filter(emp => emp.is_active);
            const stats = calculateStats(currentSchedule, activeEmployees);

            // 渲染统计卡片
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${activeEmployees.length}</div>
                    <div class="stat-label">总员工数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.totalWorkDays}</div>
                    <div class="stat-label">总工作日</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.totalRestDays}</div>
                    <div class="stat-label">总休息日</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.averageRestDays.toFixed(1)}</div>
                    <div class="stat-label">平均休息天数</div>
                </div>
            `;

            // 渲染详细统计表格
            let detailHTML = `
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-clock"></i>
                            员工排班详情
                        </h3>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>岗位</th>
                                    <th>姓名</th>
                                    <th>工作天数</th>
                                    <th>休息天数</th>
                                    <th>休息率</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            Object.keys(DEPARTMENTS).forEach(deptKey => {
                const dept = DEPARTMENTS[deptKey];
                const deptEmployees = activeEmployees.filter(emp => emp.department === deptKey);

                deptEmployees.forEach((emp, index) => {
                    const empStats = stats.employeeStats[emp.id];
                    if (empStats) {
                        detailHTML += `
                            <tr>
                                ${index === 0 ? `<td rowspan="${deptEmployees.length}" style="vertical-align: middle; background: ${dept.color}20; color: ${dept.color}; font-weight: 600;">${dept.name}</td>` : ''}
                                <td>${emp.name}</td>
                                <td>${empStats.workDays}</td>
                                <td>${empStats.restDays}</td>
                                <td>${(empStats.restDays / 7 * 100).toFixed(1)}%</td>
                            </tr>
                        `;
                    }
                });
            });

            detailHTML += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            detailedStats.innerHTML = detailHTML;
        }

        // 计算统计数据
        function calculateStats(schedule, employees) {
            const stats = {
                totalWorkDays: 0,
                totalRestDays: 0,
                averageRestDays: 0,
                employeeStats: {}
            };

            employees.forEach(emp => {
                let workDays = 0;
                let restDays = 0;

                schedule.days.forEach(day => {
                    const assignment = day.assignments[emp.id];
                    if (assignment === 'work') {
                        workDays++;
                    } else if (assignment === 'rest') {
                        restDays++;
                    }
                });

                stats.employeeStats[emp.id] = {
                    workDays: workDays,
                    restDays: restDays
                };

                stats.totalWorkDays += workDays;
                stats.totalRestDays += restDays;
            });

            stats.averageRestDays = employees.length > 0 ? stats.totalRestDays / employees.length : 0;

            return stats;
        }

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.classList.add('show');

            setTimeout(() => {
                messageEl.classList.remove('show');
            }, 3000);
        }

        // 导出数据功能
        function exportData() {
            const exportEmployees = document.getElementById('exportEmployees').checked;
            const exportSchedule = document.getElementById('exportSchedule').checked;

            if (!exportEmployees && !exportSchedule) {
                showMessage('请至少选择一项导出内容', 'warning');
                return;
            }

            const data = {
                version: '1.0',
                exportTime: new Date().toISOString(),
                exportBy: 'ThreeShiftScheduleSystem'
            };

            if (exportEmployees) {
                data.employees = employees;
            }

            if (exportSchedule && currentSchedule) {
                data.schedule = currentSchedule;
            }

            try {
                const jsonString = JSON.stringify(data, null, 2);
                const blob = new Blob([jsonString], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `schedule_data_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                const statusEl = document.getElementById('exportStatus');
                statusEl.innerHTML = `<i class="fas fa-check-circle" style="color: #27ae60;"></i> 数据导出成功！文件已保存到下载文件夹`;
                statusEl.style.color = '#27ae60';

                showMessage('数据导出成功', 'success');
            } catch (error) {
                console.error('导出失败:', error);
                showMessage('导出失败: ' + error.message, 'error');

                const statusEl = document.getElementById('exportStatus');
                statusEl.innerHTML = `<i class="fas fa-exclamation-circle" style="color: #e74c3c;"></i> 导出失败: ${error.message}`;
                statusEl.style.color = '#e74c3c';
            }
        }

        // 处理文件选择
        let selectedFile = null;
        function handleFileSelect(event) {
            const file = event.target.files[0];
            const importBtn = document.getElementById('importBtn');
            const statusEl = document.getElementById('importStatus');
            const previewEl = document.getElementById('dataPreview');

            if (file) {
                if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
                    statusEl.innerHTML = `<i class="fas fa-exclamation-triangle" style="color: #f39c12;"></i> 请选择JSON格式的文件`;
                    statusEl.style.color = '#f39c12';
                    importBtn.disabled = true;
                    selectedFile = null;
                    previewEl.style.display = 'none';
                    return;
                }

                selectedFile = file;
                importBtn.disabled = false;

                // 预览文件内容
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = JSON.parse(e.target.result);

                        // 验证数据格式
                        const validation = validateImportData(data);
                        if (!validation.valid) {
                            statusEl.innerHTML = `<i class="fas fa-exclamation-circle" style="color: #e74c3c;"></i> ${validation.error}`;
                            statusEl.style.color = '#e74c3c';
                            importBtn.disabled = true;
                            selectedFile = null;
                            previewEl.style.display = 'none';
                            return;
                        }

                        // 显示预览
                        const previewData = {
                            文件信息: {
                                版本: data.version || '未知',
                                导出时间: data.exportTime || '未知',
                                导出来源: data.exportBy || '未知'
                            }
                        };

                        if (data.employees) {
                            previewData.员工数据 = {
                                总数: data.employees.length,
                                电流线: data.employees.filter(emp => emp.department === 'current').length,
                                电压线: data.employees.filter(emp => emp.department === 'voltage').length,
                                资料组: data.employees.filter(emp => emp.department === 'data').length,
                                启用: data.employees.filter(emp => emp.is_active).length,
                                停用: data.employees.filter(emp => !emp.is_active).length
                            };
                        }

                        if (data.schedule) {
                            const scheduleWeek = new Date(data.schedule.week_start);
                            previewData.排班数据 = {
                                周次: `${scheduleWeek.getMonth() + 1}月${scheduleWeek.getDate()}日开始的一周`,
                                生成时间: data.schedule.generated_at || '未知',
                                天数: data.schedule.days ? data.schedule.days.length : 0
                            };
                        }

                        document.getElementById('previewContent').textContent = JSON.stringify(previewData, null, 2);
                        previewEl.style.display = 'block';

                        statusEl.innerHTML = `<i class="fas fa-check-circle" style="color: #27ae60;"></i> 文件格式正确，可以导入`;
                        statusEl.style.color = '#27ae60';

                    } catch (error) {
                        statusEl.innerHTML = `<i class="fas fa-exclamation-circle" style="color: #e74c3c;"></i> 文件格式错误: ${error.message}`;
                        statusEl.style.color = '#e74c3c';
                        importBtn.disabled = true;
                        selectedFile = null;
                        previewEl.style.display = 'none';
                    }
                };
                reader.readAsText(file);

            } else {
                selectedFile = null;
                importBtn.disabled = true;
                statusEl.innerHTML = '';
                previewEl.style.display = 'none';
            }
        }

        // 验证导入数据格式
        function validateImportData(data) {
            if (!data || typeof data !== 'object') {
                return { valid: false, error: '数据格式无效' };
            }

            // 验证员工数据
            if (data.employees) {
                if (!Array.isArray(data.employees)) {
                    return { valid: false, error: '员工数据格式错误' };
                }

                for (let emp of data.employees) {
                    if (!emp.id || !emp.name || !emp.department) {
                        return { valid: false, error: '员工数据缺少必要字段' };
                    }
                    if (!['current', 'voltage', 'data'].includes(emp.department)) {
                        return { valid: false, error: '员工岗位数据无效' };
                    }
                }
            }

            // 验证排班数据
            if (data.schedule) {
                if (!data.schedule.week_start || !data.schedule.days) {
                    return { valid: false, error: '排班数据格式错误' };
                }
                if (!Array.isArray(data.schedule.days) || data.schedule.days.length !== 7) {
                    return { valid: false, error: '排班数据天数错误' };
                }
            }

            return { valid: true };
        }

        // 导入数据功能
        function importData() {
            if (!selectedFile) {
                showMessage('请先选择要导入的文件', 'warning');
                return;
            }

            if (!confirm('导入数据将覆盖当前所有数据，确定要继续吗？\n\n建议先导出当前数据作为备份。')) {
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    // 再次验证数据
                    const validation = validateImportData(data);
                    if (!validation.valid) {
                        showMessage('导入失败: ' + validation.error, 'error');
                        return;
                    }

                    let importedItems = [];

                    // 导入员工数据
                    if (data.employees) {
                        employees = data.employees;
                        employeeManager.saveEmployees(employees);
                        importedItems.push(`${employees.length}名员工`);
                    }

                    // 导入排班数据
                    if (data.schedule) {
                        currentSchedule = data.schedule;
                        scheduleAlgorithm.saveSchedule(currentSchedule);
                        importedItems.push('排班表');
                    }

                    // 刷新界面
                    renderDepartments();
                    renderScheduleTable();
                    refreshStats();

                    const statusEl = document.getElementById('importStatus');
                    statusEl.innerHTML = `<i class="fas fa-check-circle" style="color: #27ae60;"></i> 成功导入: ${importedItems.join('、')}`;
                    statusEl.style.color = '#27ae60';

                    showMessage(`数据导入成功！已导入: ${importedItems.join('、')}`, 'success');

                    // 清除文件选择
                    document.getElementById('importFile').value = '';
                    document.getElementById('importBtn').disabled = true;
                    document.getElementById('dataPreview').style.display = 'none';
                    selectedFile = null;

                } catch (error) {
                    console.error('导入失败:', error);
                    showMessage('导入失败: ' + error.message, 'error');

                    const statusEl = document.getElementById('importStatus');
                    statusEl.innerHTML = `<i class="fas fa-exclamation-circle" style="color: #e74c3c;"></i> 导入失败: ${error.message}`;
                    statusEl.style.color = '#e74c3c';
                }
            };
            reader.readAsText(selectedFile);
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(e) {
            // ESC键关闭模态框
            if (e.key === 'Escape') {
                hideAddEmployeeModal();
                hideEditEmployeeModal();
            }

            // Enter键提交表单
            if (e.key === 'Enter') {
                if (document.getElementById('addEmployeeModal').style.display === 'flex') {
                    e.preventDefault();
                    addEmployee();
                } else if (document.getElementById('editEmployeeModal').style.display === 'flex') {
                    e.preventDefault();
                    updateEmployee();
                }
            }
        });

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                hideAddEmployeeModal();
                hideEditEmployeeModal();
            }
        });
    </script>
</body>
</html>

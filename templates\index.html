<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能排班系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 头部 -->
        <header class="header">
            <div class="container">
                <h1 class="title">
                    <i class="fas fa-calendar-alt"></i>
                    智能排班系统
                </h1>
                <div class="header-actions">
                    <button id="generateBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        生成排班
                    </button>
                </div>
            </div>
        </header>

        <!-- 导航栏 -->
        <nav class="nav">
            <div class="container">
                <div class="nav-tabs">
                    <button class="nav-tab active" data-tab="employees">
                        <i class="fas fa-users"></i>
                        员工管理
                    </button>
                    <button class="nav-tab" data-tab="schedule">
                        <i class="fas fa-calendar"></i>
                        排班表
                    </button>
                    <button class="nav-tab" data-tab="stats">
                        <i class="fas fa-chart-bar"></i>
                        统计分析
                    </button>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <main class="main">
            <div class="container">
                <!-- 员工管理页面 -->
                <div id="employees-tab" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <h2>员工管理</h2>
                            <button id="addEmployeeBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                添加员工
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="employeesList" class="employees-list">
                                <!-- 员工列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 排班表页面 -->
                <div id="schedule-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2>排班表</h2>
                            <div class="week-selector">
                                <button id="prevWeekBtn" class="btn btn-secondary">
                                    <i class="fas fa-chevron-left"></i>
                                    上一周
                                </button>
                                <span id="currentWeek" class="current-week"></span>
                                <button id="nextWeekBtn" class="btn btn-secondary">
                                    下一周
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="scheduleTable" class="schedule-table">
                                <!-- 排班表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计分析页面 -->
                <div id="stats-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h2>统计分析</h2>
                        </div>
                        <div class="card-body">
                            <div id="validationResult" class="validation-result">
                                <!-- 验证结果将在这里显示 -->
                            </div>
                            <div id="statsTable" class="stats-table">
                                <!-- 统计表格将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 添加员工模态框 -->
    <div id="addEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加员工</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addEmployeeForm">
                    <div class="form-group">
                        <label for="employeeName">员工姓名</label>
                        <input type="text" id="employeeName" name="name" required maxlength="20">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">确定</button>
                        <button type="button" class="btn btn-secondary modal-close">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑员工模态框 -->
    <div id="editEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑员工</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editEmployeeForm">
                    <input type="hidden" id="editEmployeeId">
                    <div class="form-group">
                        <label for="editEmployeeName">员工姓名</label>
                        <input type="text" id="editEmployeeName" name="name" required maxlength="20">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">确定</button>
                        <button type="button" class="btn btn-secondary modal-close">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message"></div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>

# 智能排班系统 - 纯前端版

一个完全基于前端技术的排班系统，无需后端服务器，直接在浏览器中运行。支持自动排班算法，确保每个人周日休息，工作日16:30和20:30班次平均分配。

## 🎯 特点

### ✨ 纯前端实现
- **零依赖**：无需安装任何软件或服务器
- **即开即用**：双击HTML文件即可使用
- **本地存储**：数据保存在浏览器本地存储中
- **离线可用**：无需网络连接

### 📋 完整功能
- ✅ 员工管理（增删改查、启用停用）
- ✅ 智能排班算法（周日休息、班次平均分配）
- ✅ 可视化排班表（支持手动调整）
- ✅ 统计分析（验证规则、数据统计）
- ✅ 响应式设计（支持手机、平板、电脑）

### 🔧 技术栈
- **HTML5** - 页面结构
- **CSS3** - 样式设计
- **JavaScript (ES6+)** - 业务逻辑
- **localStorage** - 数据存储
- **Font Awesome** - 图标库

## 🚀 使用方法

### 方法一：直接打开文件
1. 下载 `index_standalone.html` 文件
2. 双击文件在浏览器中打开
3. 开始使用排班系统

### 方法二：通过浏览器打开
1. 在浏览器地址栏输入：`file:///完整路径/index_standalone.html`
2. 回车打开系统

## 📖 使用指南

### 1. 员工管理
- 系统默认包含6个示例员工
- 点击"添加员工"可以添加新员工
- 可以编辑员工姓名
- 可以启用/停用员工（停用的员工不参与排班）
- 可以删除不需要的员工

### 2. 生成排班
- 点击"生成排班"按钮自动创建排班表
- 系统会确保所有规则得到满足：
  - 每个人周日必须休息
  - 16:30和20:30班次尽量平均分配
- 可以使用周选择器切换不同周次

### 3. 手动调整
- 在排班表中点击班次下拉框可以手动调整
- 周日固定为休息日，不可修改
- 修改会实时保存到本地存储

### 4. 统计分析
- 查看排班验证结果
- 查看每个员工的班次分配统计
- 查看整体排班数据汇总

## 💾 数据存储

- 员工数据自动保存到浏览器的localStorage中
- 数据在同一浏览器中持久保存
- 清除浏览器数据会重置为默认员工
- 排班数据目前仅在内存中，刷新页面会重新生成

## 🔄 与后端版本对比

| 功能 | 纯前端版 | 后端版 |
|------|----------|--------|
| 部署难度 | ⭐ 极简单 | ⭐⭐⭐ 需要Python环境 |
| 使用便利性 | ⭐⭐⭐ 双击即用 | ⭐⭐ 需要启动服务器 |
| 数据持久化 | ⭐⭐ 浏览器本地 | ⭐⭐⭐ 文件/数据库 |
| 多用户支持 | ❌ 单用户 | ✅ 多用户 |
| 排班功能 | ✅ 完全一致 | ✅ 完全一致 |
| 界面体验 | ✅ 完全一致 | ✅ 完全一致 |

## 🌟 适用场景

### 推荐使用纯前端版的情况：
- 小型团队（10人以下）
- 个人或家庭使用
- 临时排班需求
- 不想安装软件
- 需要离线使用

### 推荐使用后端版的情况：
- 中大型团队
- 需要多人协作
- 需要数据备份
- 有服务器环境

## 🔧 自定义配置

### 修改默认员工
在HTML文件中找到 `getDefaultEmployees()` 方法，修改默认员工列表：

```javascript
getDefaultEmployees() {
    return [
        { id: 'emp1', name: '张三', is_active: true },
        { id: 'emp2', name: '李四', is_active: true },
        // 添加更多员工...
    ];
}
```

### 修改班次时间
在HTML文件中找到 `SHIFT_TYPES` 对象，修改班次标签：

```javascript
this.SHIFT_TYPES = {
    'early': '早班(16:30)',
    'late': '晚班(20:30)', 
    'off': '休息'
};
```

## 📱 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 🆘 常见问题

**Q: 数据会丢失吗？**
A: 员工数据保存在浏览器本地存储中，除非主动清除浏览器数据，否则不会丢失。

**Q: 可以导出数据吗？**
A: 当前版本不支持导出，如需此功能请使用后端版本。

**Q: 支持多人同时使用吗？**
A: 不支持，每个浏览器独立使用。如需多人协作请使用后端版本。

**Q: 可以修改排班规则吗？**
A: 可以修改HTML文件中的JavaScript代码来自定义规则。

## 📄 许可证

MIT License

---

**享受简单高效的排班体验！** 🎉

# 智能排班系统

一个基于Python Flask的网页版排班系统，支持自动排班算法，确保每个人周日休息，工作日16:30和20:30班次平均分配。

## 功能特点

### 🎯 核心功能
- **智能排班算法**：自动生成排班表，确保规则合规
- **员工管理**：添加、编辑、删除、启用/停用员工
- **排班表管理**：可视化排班表，支持手动调整
- **统计分析**：实时验证排班规则，显示统计数据

### 📋 排班规则
- ✅ 每个人周日必须休息
- ✅ 工作日分为两个班次：16:30下班（早班）和20:30下班（晚班）
- ✅ 早晚班次尽量平均分配给所有员工
- ✅ 支持手动调整特殊需求

### 🎨 界面特色
- 现代化响应式设计
- 直观的用户交互
- 实时数据验证
- 中文本地化

## 技术栈

- **后端**：Python Flask + Flask-CORS
- **前端**：HTML5 + CSS3 + JavaScript (ES6+)
- **数据存储**：JSON文件（本地存储）
- **UI框架**：原生CSS + Font Awesome图标

## 快速开始

### 环境要求
- Python 3.7+
- pip

### 安装步骤

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **启动应用**
```bash
python app.py
```

3. **访问应用**
打开浏览器访问：http://localhost:5000

## 使用指南

### 1. 员工管理
- 在"员工管理"页面添加员工
- 可以编辑员工姓名
- 可以启用/停用员工（停用的员工不参与排班）
- 可以删除不需要的员工

### 2. 生成排班
- 点击"生成排班"按钮自动创建排班表
- 系统会确保所有规则得到满足
- 可以使用周选择器切换不同周次

### 3. 手动调整
- 在排班表中点击班次下拉框可以手动调整
- 周日固定为休息日，不可修改
- 修改会实时保存

### 4. 统计分析
- 查看排班验证结果
- 查看每个员工的班次分配统计
- 查看整体排班数据汇总

## 项目结构

```
排班系统/
├── app.py                 # Flask主应用
├── employee_manager.py    # 员工管理模块
├── schedule_algorithm.py  # 排班算法模块
├── requirements.txt       # Python依赖
├── templates/
│   └── index.html        # 主页模板
├── static/
│   ├── style.css         # 样式文件
│   └── script.js         # JavaScript逻辑
└── employees.json        # 员工数据文件（自动生成）
```

## API接口

### 员工管理
- `GET /api/employees` - 获取所有员工
- `POST /api/employees` - 添加员工
- `PUT /api/employees/<id>` - 更新员工
- `DELETE /api/employees/<id>` - 删除员工

### 排班管理
- `POST /api/schedule/generate` - 生成排班表
- `POST /api/schedule/validate` - 验证排班表
- `POST /api/schedule/stats` - 获取排班统计

## 自定义配置

### 修改班次时间
在 `schedule_algorithm.py` 中修改 `SHIFT_TYPES` 字典：
```python
SHIFT_TYPES = {
    'early': '早班(16:30)',
    'late': '晚班(20:30)', 
    'off': '休息'
}
```

### 修改默认员工
在 `employee_manager.py` 中修改 `_get_default_employees` 方法。

## 注意事项

- 本系统使用JSON文件存储数据，适合小型团队使用
- 生产环境建议使用数据库存储
- 系统会自动保存员工数据到 `employees.json` 文件
- 排班数据目前仅在内存中，刷新页面会丢失，如需持久化请自行扩展

## 许可证

MIT License

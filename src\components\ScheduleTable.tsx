import React from 'react';
import { format } from 'date-fns';
import { Employee, WeekSchedule, ShiftType } from '../types';

interface ScheduleTableProps {
  schedule: WeekSchedule | null;
  employees: Employee[];
  onShiftChange: (employeeId: string, dayIndex: number, shift: ShiftType) => void;
}

const shiftLabels: Record<ShiftType, string> = {
  early: '早班(16:30)',
  late: '晚班(20:30)',
  off: '休息'
};

const shiftColors: Record<ShiftType, string> = {
  early: 'bg-blue-100 text-blue-800 border-blue-200',
  late: 'bg-orange-100 text-orange-800 border-orange-200',
  off: 'bg-gray-100 text-gray-600 border-gray-200'
};

export function ScheduleTable({ schedule, employees, onShiftChange }: ScheduleTableProps) {
  if (!schedule) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8 text-gray-500">
          请先生成排班表
        </div>
      </div>
    );
  }

  const activeEmployees = employees.filter(emp => emp.isActive);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">
          排班表 - {format(schedule.weekStart, 'yyyy年MM月dd日')} 周
        </h2>
        <p className="text-sm text-gray-600">
          点击班次可以手动调整，周日默认为休息日
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="border border-gray-300 bg-gray-50 px-4 py-3 text-left font-medium text-gray-700">
                员工
              </th>
              {schedule.days.map((day, index) => (
                <th
                  key={index}
                  className={`border border-gray-300 px-4 py-3 text-center font-medium ${
                    day.weekDay === 'sunday'
                      ? 'bg-red-50 text-red-700'
                      : 'bg-gray-50 text-gray-700'
                  }`}
                >
                  <div className="text-sm">
                    {format(day.date, 'MM/dd')}
                  </div>
                  <div className="text-xs">
                    {format(day.date, 'EEEE')}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {activeEmployees.map((employee) => (
              <tr key={employee.id}>
                <td className="border border-gray-300 bg-gray-50 px-4 py-3 font-medium text-gray-700">
                  {employee.name}
                </td>
                {schedule.days.map((day, dayIndex) => {
                  const shift = day.assignments[employee.id] || 'off';
                  const isWeekend = day.weekDay === 'sunday';
                  
                  return (
                    <td key={dayIndex} className="border border-gray-300 p-2">
                      {isWeekend ? (
                        <div className="text-center py-2 px-3 rounded-md bg-red-50 text-red-600 border border-red-200">
                          休息
                        </div>
                      ) : (
                        <select
                          value={shift}
                          onChange={(e) => onShiftChange(employee.id, dayIndex, e.target.value as ShiftType)}
                          className={`w-full py-2 px-3 rounded-md border text-center text-sm font-medium transition-colors ${shiftColors[shift]} hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        >
                          <option value="early">早班(16:30)</option>
                          <option value="late">晚班(20:30)</option>
                          <option value="off">休息</option>
                        </select>
                      )}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {activeEmployees.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          没有活跃的员工，请先添加并启用员工
        </div>
      )}
    </div>
  );
}

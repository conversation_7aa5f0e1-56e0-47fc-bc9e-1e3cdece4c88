/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON>l, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-bottom: 1px solid #e0e0e0;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.title {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
}

.title i {
    margin-right: 0.5rem;
    color: #3498db;
}

/* 导航栏样式 */
.nav {
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
}

.nav-tabs {
    display: flex;
    gap: 0;
}

.nav-tab {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    color: #666;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-tab:hover {
    color: #3498db;
    background-color: #f8f9fa;
}

.nav-tab.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background-color: #f8f9fa;
}

/* 主内容区样式 */
.main {
    padding: 2rem 0;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 卡片样式 */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    color: #2c3e50;
    font-size: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #229954;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 员工列表样式 */
.employees-list {
    display: grid;
    gap: 1rem;
}

.employee-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: #f8f9fa;
}

.employee-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.employee-name {
    font-weight: 500;
    color: #2c3e50;
}

.employee-status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.employee-status.active {
    background-color: #d4edda;
    color: #155724;
}

.employee-status.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.employee-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 周选择器样式 */
.week-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.current-week {
    font-weight: 500;
    color: #2c3e50;
    min-width: 200px;
    text-align: center;
}

/* 排班表样式 */
.schedule-table {
    overflow-x: auto;
}

.schedule-table table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.schedule-table th,
.schedule-table td {
    border: 1px solid #e0e0e0;
    padding: 0.75rem;
    text-align: center;
}

.schedule-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.schedule-table .sunday {
    background-color: #fff5f5;
    color: #e74c3c;
}

.shift-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

.shift-early {
    background-color: #e3f2fd;
    color: #1976d2;
}

.shift-late {
    background-color: #fff3e0;
    color: #f57c00;
}

.shift-off {
    background-color: #f5f5f5;
    color: #666;
}

/* 统计表格样式 */
.stats-table table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.stats-table th,
.stats-table td {
    border: 1px solid #e0e0e0;
    padding: 0.75rem;
    text-align: center;
}

.stats-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.stats-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.stats-badge.early {
    background-color: #e3f2fd;
    color: #1976d2;
}

.stats-badge.late {
    background-color: #fff3e0;
    color: #f57c00;
}

.stats-badge.off {
    background-color: #f5f5f5;
    color: #666;
}

/* 验证结果样式 */
.validation-result {
    margin-bottom: 2rem;
}

.validation-item {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.validation-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.validation-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.validation-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 1.5rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* 加载和消息样式 */
.loading {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.loading.show {
    display: flex;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #fff;
    margin-top: 1rem;
    font-size: 1rem;
}

.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    color: #fff;
    font-weight: 500;
    z-index: 1500;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.message.show {
    transform: translateX(0);
}

.message.success {
    background-color: #27ae60;
}

.message.error {
    background-color: #e74c3c;
}

.message.warning {
    background-color: #f39c12;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .week-selector {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .employee-item {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .employee-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

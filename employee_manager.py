import json
import os
import uuid
from typing import List, Dict, Any

class EmployeeManager:
    """员工管理类"""
    
    def __init__(self, data_file='employees.json'):
        self.data_file = data_file
        self.employees = self._load_employees()
    
    def _load_employees(self) -> List[Dict[str, Any]]:
        """从文件加载员工数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                pass
        
        # 返回默认员工数据
        return self._get_default_employees()
    
    def _save_employees(self) -> None:
        """保存员工数据到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.employees, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"保存员工数据失败: {e}")
    
    def _get_default_employees(self) -> List[Dict[str, Any]]:
        """获取默认员工数据"""
        return [
            {'id': 'emp1', 'name': '张三', 'is_active': True},
            {'id': 'emp2', 'name': '李四', 'is_active': True},
            {'id': 'emp3', 'name': '王五', 'is_active': True},
            {'id': 'emp4', 'name': '赵六', 'is_active': True},
            {'id': 'emp5', 'name': '钱七', 'is_active': True},
            {'id': 'emp6', 'name': '孙八', 'is_active': True}
        ]
    
    def _generate_id(self) -> str:
        """生成唯一ID"""
        return str(uuid.uuid4())[:8]
    
    def _validate_name(self, name: str, exclude_id: str = None) -> None:
        """验证员工姓名"""
        if not name or not name.strip():
            raise ValueError('员工姓名不能为空')
        
        if len(name.strip()) > 20:
            raise ValueError('员工姓名不能超过20个字符')
        
        # 检查重名
        for emp in self.employees:
            if emp['name'] == name.strip() and emp['id'] != exclude_id:
                raise ValueError('员工姓名已存在')
    
    def get_all_employees(self) -> List[Dict[str, Any]]:
        """获取所有员工"""
        return self.employees.copy()
    
    def get_active_employees(self) -> List[Dict[str, Any]]:
        """获取活跃员工"""
        return [emp for emp in self.employees if emp.get('is_active', True)]
    
    def get_employee_by_id(self, employee_id: str) -> Dict[str, Any]:
        """根据ID获取员工"""
        for emp in self.employees:
            if emp['id'] == employee_id:
                return emp.copy()
        raise ValueError('员工不存在')
    
    def add_employee(self, name: str) -> Dict[str, Any]:
        """添加员工"""
        self._validate_name(name)
        
        employee = {
            'id': self._generate_id(),
            'name': name.strip(),
            'is_active': True
        }
        
        self.employees.append(employee)
        self._save_employees()
        
        return employee.copy()
    
    def update_employee(self, employee_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新员工信息"""
        employee = None
        for emp in self.employees:
            if emp['id'] == employee_id:
                employee = emp
                break
        
        if not employee:
            raise ValueError('员工不存在')
        
        # 更新姓名
        if 'name' in data:
            self._validate_name(data['name'], employee_id)
            employee['name'] = data['name'].strip()
        
        # 更新状态
        if 'is_active' in data:
            employee['is_active'] = bool(data['is_active'])
        
        self._save_employees()
        return employee.copy()
    
    def delete_employee(self, employee_id: str) -> None:
        """删除员工"""
        for i, emp in enumerate(self.employees):
            if emp['id'] == employee_id:
                del self.employees[i]
                self._save_employees()
                return
        
        raise ValueError('员工不存在')
    
    def toggle_employee_status(self, employee_id: str) -> Dict[str, Any]:
        """切换员工状态"""
        employee = None
        for emp in self.employees:
            if emp['id'] == employee_id:
                employee = emp
                break
        
        if not employee:
            raise ValueError('员工不存在')
        
        employee['is_active'] = not employee.get('is_active', True)
        self._save_employees()
        
        return employee.copy()

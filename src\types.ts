// 员工信息类型
export interface Employee {
  id: string;
  name: string;
  isActive: boolean;
}

// 班次类型
export type ShiftType = 'early' | 'late' | 'off'; // 16:30下班 | 20:30下班 | 休息

// 工作日类型
export type WeekDay = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

// 单日排班
export interface DaySchedule {
  date: Date;
  weekDay: WeekDay;
  assignments: Record<string, ShiftType>; // employeeId -> shiftType
}

// 周排班
export interface WeekSchedule {
  weekStart: Date;
  days: DaySchedule[];
}

// 排班统计
export interface ScheduleStats {
  employeeId: string;
  employeeName: string;
  earlyShifts: number;
  lateShifts: number;
  offDays: number;
  sundayOff: boolean;
}

// 排班验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
